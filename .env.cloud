# Atlas API - Cloud Environment Variables
# SECURITY: DO NOT COMMIT TO GIT

# Google Earth Engine Authentication
GOOGLE_CLOUD_PROJECT=grand-airport-464420-e3
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY_ID=5ff05269086d4d1425998a76df3d603f81cb45d4
GOOGLE_CLIENT_ID=116275573697305905318
GOOGLE_PRIVATE_KEY="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
MAX_AOI_AREA_KM2=100.0
REQUEST_TIMEOUT=300
OVERPASS_API_URL=https://overpass-api.de/api/interpreter
OVERPASS_RATE_LIMIT=0.5
