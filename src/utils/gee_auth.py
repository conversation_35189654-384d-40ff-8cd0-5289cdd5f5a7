"""
Google Earth Engine authentication utility for local and cloud deployments
"""

import os
import ee
from typing import Optional
from src.core.config import get_settings


def initialize_earth_engine() -> bool:
    """
    Initialize Google Earth Engine with appropriate authentication method

    Returns:
        bool: True if initialization successful, False otherwise
    """
    settings = get_settings()

    try:
        # Get credentials path (handles both local file and cloud environment variable)
        credentials_path = settings.get_google_credentials_path()

        if credentials_path:
            # Set the credentials path in environment if not already set
            if 'GOOGLE_APPLICATION_CREDENTIALS' not in os.environ:
                os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path

            # Initialize Earth Engine with service account credentials
            # This is the correct way for service accounts with proper OAuth scopes
            try:
                # Try service account authentication first
                import json
                with open(credentials_path, 'r') as f:
                    key_data = json.load(f)

                credentials = ee.ServiceAccountCredentials(
                    key_data['client_email'],
                    credentials_path
                )

                # Initialize with explicit credentials and project
                ee.Initialize(credentials, project=settings.google_cloud_project)
                print(f"✓ Google Earth Engine initialized with service account: {key_data['client_email']}")
                print(f"✓ Project: {settings.google_cloud_project}")
                return True

            except Exception as service_account_error:
                print(f"Service account authentication failed: {service_account_error}")

                # Fallback to default authentication (for local development)
                try:
                    ee.Initialize(project=settings.google_cloud_project)
                    print(f"✓ Google Earth Engine initialized with default credentials")
                    print(f"✓ Project: {settings.google_cloud_project}")
                    return True
                except Exception as default_error:
                    print(f"Default authentication also failed: {default_error}")
                    raise default_error

        else:
            print("⚠️  No Google Earth Engine credentials found. Some data sources will be unavailable.")
            return False

    except Exception as e:
        print(f"❌ Failed to initialize Google Earth Engine: {e}")
        print("Common causes:")
        print("  • Service account doesn't have Earth Engine access")
        print("  • Invalid OAuth scopes in service account")
        print("  • Project not enabled for Earth Engine")
        print("  • Malformed credentials file")
        return False


def validate_service_account_setup() -> dict:
    """
    Validate Google service account setup for Earth Engine

    Returns:
        dict: Validation results with detailed information
    """
    settings = get_settings()
    validation_results = {
        'valid': False,
        'issues': [],
        'recommendations': [],
        'service_account_info': {}
    }

    try:
        credentials_path = settings.get_google_credentials_path()

        if not credentials_path:
            validation_results['issues'].append("No credentials file found")
            validation_results['recommendations'].append("Set up Google service account credentials")
            return validation_results

        # Read and validate service account file
        import json
        with open(credentials_path, 'r') as f:
            key_data = json.load(f)

        # Check required fields
        required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email', 'client_id']
        missing_fields = [field for field in required_fields if field not in key_data]

        if missing_fields:
            validation_results['issues'].append(f"Missing required fields: {missing_fields}")

        validation_results['service_account_info'] = {
            'email': key_data.get('client_email', 'unknown'),
            'project_id': key_data.get('project_id', 'unknown'),
            'type': key_data.get('type', 'unknown')
        }

        # Check if it's a service account
        if key_data.get('type') != 'service_account':
            validation_results['issues'].append(f"Expected service account, got: {key_data.get('type')}")
            validation_results['recommendations'].append("Use a service account key, not user credentials")

        # Validate project ID matches
        if key_data.get('project_id') != settings.google_cloud_project:
            validation_results['issues'].append(
                f"Project ID mismatch: credentials={key_data.get('project_id')}, config={settings.google_cloud_project}"
            )

        if not validation_results['issues']:
            validation_results['valid'] = True
            validation_results['recommendations'].extend([
                "Service account file appears valid",
                "Ensure the service account has Earth Engine access enabled",
                "Check that the project is registered for Earth Engine"
            ])
        else:
            validation_results['recommendations'].extend([
                "Fix the issues above",
                "Regenerate service account key if necessary",
                "Ensure service account has 'Earth Engine Resource Viewer' role"
            ])

    except Exception as e:
        validation_results['issues'].append(f"Error reading credentials: {e}")
        validation_results['recommendations'].append("Check credentials file format and permissions")

    return validation_results


def test_earth_engine_connection() -> bool:
    """
    Test Google Earth Engine connection

    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        # Validate service account setup first
        validation = validate_service_account_setup()
        if not validation['valid']:
            print("❌ Service account validation failed:")
            for issue in validation['issues']:
                print(f"   • {issue}")
            return False

        # Simple test to verify Earth Engine is working
        print("🧪 Testing Earth Engine connection...")
        test_image = ee.Image("USGS/SRTMGL1_003")
        test_info = test_image.getInfo()
        print("✅ Google Earth Engine connection test: SUCCESS")
        return True

    except Exception as e:
        print(f"❌ Google Earth Engine connection test failed: {e}")

        # Provide specific error guidance
        error_str = str(e).lower()
        if 'oauth' in error_str or 'scope' in error_str:
            print("💡 OAuth/Scope Error Solutions:")
            print("   1. Ensure service account has 'Earth Engine Resource Viewer' role")
            print("   2. Enable Earth Engine API for your project")
            print("   3. Register your project for Earth Engine access")
            print("   4. Wait up to 24 hours for permissions to propagate")
        elif 'project' in error_str:
            print("💡 Project Error Solutions:")
            print("   1. Verify project ID is correct")
            print("   2. Enable Earth Engine API in Google Cloud Console")
            print("   3. Ensure project is registered for Earth Engine")
        elif 'authentication' in error_str or 'credentials' in error_str:
            print("💡 Authentication Error Solutions:")
            print("   1. Regenerate service account key")
            print("   2. Check credentials file format")
            print("   3. Verify service account email is correct")

        return False