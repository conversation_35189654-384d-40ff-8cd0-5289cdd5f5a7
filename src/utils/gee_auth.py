"""
Google Earth Engine authentication utility for local and cloud deployments
"""

import os
import ee
import json
import logging
import traceback
from typing import Optional, Dict, Any
from src.core.config import get_settings

# Set up detailed logging for Google API responses
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Enable detailed HTTP logging for Google API calls
import google.auth.transport.requests
import google.auth.transport.urllib3
import urllib3

# Enable urllib3 debug logging to capture raw HTTP responses
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
logging.getLogger("urllib3.connectionpool").setLevel(logging.DEBUG)
logging.getLogger("google.auth.transport.requests").setLevel(logging.DEBUG)
logging.getLogger("google.auth.transport.urllib3").setLevel(logging.DEBUG)
logging.getLogger("google.auth._default").setLevel(logging.DEBUG)


def create_service_account_credentials_from_env() -> Optional[str]:
    """
    Create service account credentials from individual environment variables
    This is the proper way to handle secrets in HuggingFace Spaces
    """
    settings = get_settings()

    # Check if we have all required individual environment variables
    required_fields = {
        'type': 'service_account',
        'project_id': settings.google_cloud_project,
        'private_key_id': settings.google_private_key_id,
        'private_key': settings.google_private_key,
        'client_email': settings.google_service_account_email,
        'client_id': settings.google_client_id,
        'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
        'token_uri': 'https://oauth2.googleapis.com/token',
        'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs',
        'universe_domain': 'googleapis.com'
    }

    # Validate all required fields are present
    missing_fields = []
    for key, value in required_fields.items():
        if key in ['type', 'auth_uri', 'token_uri', 'auth_provider_x509_cert_url', 'universe_domain']:
            continue  # These are constants
        if not value or not str(value).strip():
            missing_fields.append(key)

    if missing_fields:
        logger.error(f"❌ Missing required Google service account fields: {missing_fields}")
        return None

    # Add the client_x509_cert_url
    required_fields['client_x509_cert_url'] = f"https://www.googleapis.com/robot/v1/metadata/x509/{settings.google_service_account_email.replace('@', '%40')}"

    try:
        # Create temporary credentials file
        import tempfile
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)

        # Log the credential structure (without sensitive data)
        logger.info("🔧 Creating service account credentials from environment variables")
        logger.info(f"   Project ID: {required_fields['project_id']}")
        logger.info(f"   Client Email: {required_fields['client_email']}")
        logger.info(f"   Client ID: {required_fields['client_id']}")
        logger.info(f"   Private Key ID: {required_fields['private_key_id'][:8]}...")
        logger.info(f"   Private Key Length: {len(required_fields['private_key'])} chars")

        # Write credentials to temporary file
        json.dump(required_fields, temp_file, indent=2)
        temp_file.close()

        logger.info(f"✅ Service account credentials file created: {temp_file.name}")
        return temp_file.name

    except Exception as e:
        logger.error(f"❌ Failed to create service account credentials: {e}")
        logger.error(f"   Error details: {traceback.format_exc()}")
        return None


def log_google_api_response(response_data: Any, context: str = "API Response"):
    """Log detailed Google API response for debugging"""
    logger.info(f"🔍 {context}:")
    logger.info(f"   Response Type: {type(response_data)}")
    logger.info(f"   Response Content: {response_data}")

    if hasattr(response_data, '__dict__'):
        logger.info(f"   Response Attributes: {response_data.__dict__}")

    if isinstance(response_data, dict):
        for key, value in response_data.items():
            logger.info(f"   {key}: {value}")


def initialize_earth_engine() -> bool:
    """
    Initialize Google Earth Engine with comprehensive error handling and API response logging

    Returns:
        bool: True if initialization successful, False otherwise
    """
    settings = get_settings()

    logger.info("🚀 Starting Google Earth Engine initialization...")

    try:
        # Method 1: Try creating credentials from individual environment variables (HF Spaces)
        credentials_path = create_service_account_credentials_from_env()

        if not credentials_path:
            # Method 2: Try existing credentials file
            credentials_path = settings.get_google_credentials_path()

        if credentials_path and os.path.exists(credentials_path):
            logger.info(f"📁 Using credentials file: {credentials_path}")

            # Set the credentials path in environment
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path

            try:
                # Read and validate the credentials file
                with open(credentials_path, 'r') as f:
                    key_data = json.load(f)

                log_google_api_response(key_data, "Service Account Key Data")

                # Create service account credentials with explicit scopes
                logger.info("🔐 Creating service account credentials...")

                # Use the correct scopes for Earth Engine
                scopes = [
                    'https://www.googleapis.com/auth/earthengine',
                    'https://www.googleapis.com/auth/earthengine.readonly',
                    'https://www.googleapis.com/auth/devstorage.full_control',
                    'https://www.googleapis.com/auth/cloud-platform'
                ]

                logger.info(f"🎯 Using OAuth scopes: {scopes}")

                # Method A: Try with explicit scopes using google.auth
                try:
                    from google.oauth2 import service_account
                    import google.auth

                    logger.info("🔧 Attempting authentication with google.oauth2.service_account...")

                    credentials = service_account.Credentials.from_service_account_file(
                        credentials_path,
                        scopes=scopes
                    )

                    logger.info("✅ Service account credentials created successfully")
                    log_google_api_response(credentials, "Google Auth Credentials Object")

                    # Initialize Earth Engine with the credentials
                    logger.info(f"🌍 Initializing Earth Engine with project: {settings.google_cloud_project}")
                    ee.Initialize(credentials, project=settings.google_cloud_project)

                    logger.info(f"✅ Google Earth Engine initialized successfully!")
                    logger.info(f"   Service Account: {key_data.get('client_email', 'unknown')}")
                    logger.info(f"   Project: {settings.google_cloud_project}")
                    return True

                except Exception as google_auth_error:
                    logger.error(f"❌ google.oauth2.service_account failed: {google_auth_error}")
                    log_google_api_response(google_auth_error, "Google Auth Error")

                    # Method B: Try with ee.ServiceAccountCredentials (legacy method)
                    try:
                        logger.info("🔧 Attempting authentication with ee.ServiceAccountCredentials...")

                        credentials = ee.ServiceAccountCredentials(
                            key_data['client_email'],
                            credentials_path
                        )

                        logger.info("✅ EE service account credentials created")
                        log_google_api_response(credentials, "EE Credentials Object")

                        # Initialize with explicit credentials and project
                        ee.Initialize(credentials, project=settings.google_cloud_project)

                        logger.info(f"✅ Google Earth Engine initialized with ee.ServiceAccountCredentials!")
                        logger.info(f"   Service Account: {key_data['client_email']}")
                        logger.info(f"   Project: {settings.google_cloud_project}")
                        return True

                    except Exception as ee_auth_error:
                        logger.error(f"❌ ee.ServiceAccountCredentials failed: {ee_auth_error}")
                        log_google_api_response(ee_auth_error, "EE Auth Error")
                        raise ee_auth_error

            except Exception as credentials_error:
                logger.error(f"❌ Service account authentication failed: {credentials_error}")
                logger.error(f"   Full error details: {traceback.format_exc()}")
                log_google_api_response(credentials_error, "Credentials Error")

                # Method 3: Try default authentication (fallback)
                try:
                    logger.info("🔧 Attempting fallback to default authentication...")
                    ee.Initialize(project=settings.google_cloud_project)
                    logger.info(f"✅ Google Earth Engine initialized with default credentials")
                    return True

                except Exception as default_error:
                    logger.error(f"❌ Default authentication also failed: {default_error}")
                    log_google_api_response(default_error, "Default Auth Error")
                    raise default_error

        else:
            logger.warning("⚠️  No Google Earth Engine credentials found.")
            logger.warning("   Some data sources will be unavailable.")
            return False

    except Exception as e:
        logger.error(f"❌ CRITICAL: Google Earth Engine initialization failed: {e}")
        logger.error(f"   Full traceback: {traceback.format_exc()}")
        log_google_api_response(e, "Critical Error")

        # Provide specific troubleshooting guidance
        error_str = str(e).lower()
        if 'serviceusage.serviceusageconsumer' in error_str:
            logger.error("🔧 SOLUTION: Add 'Service Usage Consumer' role to service account:")
            logger.error("   1. Go to Google Cloud Console → IAM & Admin → IAM")
            logger.error("   2. Find your service account")
            logger.error("   3. Add role: roles/serviceusage.serviceUsageConsumer")

        if 'oauth' in error_str or 'scope' in error_str or 'invalid_scope' in error_str:
            logger.error("🔧 SOLUTION: OAuth scope error - check service account setup:")
            logger.error("   1. Ensure service account has Earth Engine Resource Viewer role")
            logger.error("   2. Enable Earth Engine API in Google Cloud Console")
            logger.error("   3. Register project for Earth Engine at https://earthengine.google.com/")
            logger.error("   4. Check that credentials are properly formatted")

        if 'permission' in error_str:
            logger.error("🔧 SOLUTION: Permission error - add required roles:")
            logger.error("   1. roles/earthengine.viewer or roles/earthengine.writer")
            logger.error("   2. roles/serviceusage.serviceUsageConsumer")
            logger.error("   3. roles/cloudasset.viewer (if using asset operations)")

        return False


def validate_service_account_setup() -> dict:
    """
    Validate Google service account setup for Earth Engine

    Returns:
        dict: Validation results with detailed information
    """
    settings = get_settings()
    validation_results = {
        'valid': False,
        'issues': [],
        'recommendations': [],
        'service_account_info': {}
    }

    try:
        credentials_path = settings.get_google_credentials_path()

        if not credentials_path:
            validation_results['issues'].append("No credentials file found")
            validation_results['recommendations'].append("Set up Google service account credentials")
            return validation_results

        # Read and validate service account file
        import json
        with open(credentials_path, 'r') as f:
            key_data = json.load(f)

        # Check required fields
        required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email', 'client_id']
        missing_fields = [field for field in required_fields if field not in key_data]

        if missing_fields:
            validation_results['issues'].append(f"Missing required fields: {missing_fields}")

        validation_results['service_account_info'] = {
            'email': key_data.get('client_email', 'unknown'),
            'project_id': key_data.get('project_id', 'unknown'),
            'type': key_data.get('type', 'unknown')
        }

        # Check if it's a service account
        if key_data.get('type') != 'service_account':
            validation_results['issues'].append(f"Expected service account, got: {key_data.get('type')}")
            validation_results['recommendations'].append("Use a service account key, not user credentials")

        # Validate project ID matches
        if key_data.get('project_id') != settings.google_cloud_project:
            validation_results['issues'].append(
                f"Project ID mismatch: credentials={key_data.get('project_id')}, config={settings.google_cloud_project}"
            )

        if not validation_results['issues']:
            validation_results['valid'] = True
            validation_results['recommendations'].extend([
                "Service account file appears valid",
                "Ensure the service account has Earth Engine access enabled",
                "Check that the project is registered for Earth Engine"
            ])
        else:
            validation_results['recommendations'].extend([
                "Fix the issues above",
                "Regenerate service account key if necessary",
                "Ensure service account has 'Earth Engine Resource Viewer' role"
            ])

    except Exception as e:
        validation_results['issues'].append(f"Error reading credentials: {e}")
        validation_results['recommendations'].append("Check credentials file format and permissions")

    return validation_results


def test_earth_engine_connection() -> bool:
    """
    Test Google Earth Engine connection with comprehensive error logging

    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        # Validate service account setup first
        validation = validate_service_account_setup()
        if not validation['valid']:
            logger.error("❌ Service account validation failed:")
            for issue in validation['issues']:
                logger.error(f"   • {issue}")
            return False

        # Simple test to verify Earth Engine is working
        logger.info("🧪 Testing Earth Engine connection...")

        try:
            # Test 1: Basic image access
            test_image = ee.Image("USGS/SRTMGL1_003")
            logger.info("✅ Test image created successfully")

            # Test 2: Get image info (this triggers actual API call)
            logger.info("🔍 Fetching image metadata (this will test API access)...")
            test_info = test_image.getInfo()

            logger.info("✅ Google Earth Engine connection test: SUCCESS")
            log_google_api_response(test_info, "Test Image Info")
            return True

        except Exception as api_error:
            logger.error(f"❌ Earth Engine API call failed: {api_error}")
            log_google_api_response(api_error, "API Call Error")

            # Try a simpler test
            try:
                logger.info("🔍 Trying simpler connection test...")
                ee.Number(1).getInfo()
                logger.info("✅ Basic Earth Engine connection works")
                return True
            except Exception as simple_error:
                logger.error(f"❌ Even basic Earth Engine test failed: {simple_error}")
                log_google_api_response(simple_error, "Simple Test Error")
                raise simple_error

    except Exception as e:
        logger.error(f"❌ Google Earth Engine connection test failed: {e}")
        logger.error(f"   Full traceback: {traceback.format_exc()}")
        log_google_api_response(e, "Connection Test Error")

        # Provide specific error guidance based on the error
        error_str = str(e).lower()

        if 'serviceusage.serviceusageconsumer' in error_str:
            logger.error("💡 MISSING ROLE: Service Usage Consumer")
            logger.error("   SOLUTION: Add roles/serviceusage.serviceUsageConsumer to your service account")
            logger.error("   This role is required for API usage in Google Cloud projects")

        if 'oauth' in error_str or 'scope' in error_str or 'invalid_scope' in error_str:
            logger.error("💡 OAUTH/SCOPE ERROR:")
            logger.error("   1. Service account missing 'Earth Engine Resource Viewer' role")
            logger.error("   2. Earth Engine API not enabled in Google Cloud Console")
            logger.error("   3. Project not registered for Earth Engine access")
            logger.error("   4. Invalid OAuth scopes in authentication")

        if 'permission' in error_str or 'forbidden' in error_str:
            logger.error("💡 PERMISSION ERROR:")
            logger.error("   Required roles for service account:")
            logger.error("   • roles/earthengine.viewer (or earthengine.writer)")
            logger.error("   • roles/serviceusage.serviceUsageConsumer")
            logger.error("   • Project must be registered for Earth Engine")

        if 'project' in error_str:
            logger.error("💡 PROJECT ERROR:")
            logger.error("   1. Verify project ID is correct")
            logger.error("   2. Enable Earth Engine API in Google Cloud Console")
            logger.error("   3. Register project at https://earthengine.google.com/")

        if 'authentication' in error_str or 'credentials' in error_str:
            logger.error("💡 AUTHENTICATION ERROR:")
            logger.error("   1. Check service account key format")
            logger.error("   2. Verify all required fields are present")
            logger.error("   3. Ensure private key is properly formatted")

        return False