"""
Google Earth Engine authentication utility for local and cloud deployments
"""

import os
import ee
from typing import Optional
from src.core.config import get_settings


def initialize_earth_engine() -> bool:
    """
    Initialize Google Earth Engine with appropriate authentication method
    
    Returns:
        bool: True if initialization successful, False otherwise
    """
    settings = get_settings()
    
    try:
        # Get credentials path (handles both local file and cloud environment variable)
        credentials_path = settings.get_google_credentials_path()
        
        if credentials_path:
            # Set the credentials path in environment if not already set
            if 'GOOGLE_APPLICATION_CREDENTIALS' not in os.environ:
                os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
            
            # Initialize Earth Engine
            ee.Initialize(project=settings.google_cloud_project)
            print(f"Google Earth Engine initialized successfully with project: {settings.google_cloud_project}")
            return True
            
        else:
            print("Warning: No Google Earth Engine credentials found. Some data sources will be unavailable.")
            return False
            
    except Exception as e:
        print(f"Failed to initialize Google Earth Engine: {e}")
        return False


def test_earth_engine_connection() -> bool:
    """
    Test Google Earth Engine connection
    
    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        # Simple test to verify Earth Engine is working
        test_image = ee.Image("USGS/SRTMGL1_003")
        test_info = test_image.getInfo()
        print("Google Earth Engine connection test: SUCCESS")
        return True
    except Exception as e:
        print(f"Google Earth Engine connection test failed: {e}")
        return False