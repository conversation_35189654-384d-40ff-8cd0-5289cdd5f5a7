"""
Health check endpoints for monitoring service status
"""

import time
import async<PERSON>
from datetime import datetime
from typing import Dict

from fastapi import APIRouter, HTTPException

from src.api.schemas import HealthStatus, DataSourceHealth
from src.core.data_sources.google_earth_engine import GoogleEarthEngineConnector
from src.core.data_sources.openstreetmap import OpenStreetMapConnector
from src.api.schemas import DataSourceType, DataSourceConfig
from src.core.config import get_settings

router = APIRouter()
settings = get_settings()

# Service start time for uptime calculation
SERVICE_START_TIME = time.time()


@router.get("/health", response_model=HealthStatus)
async def health_check():
    """Basic health check endpoint"""
    
    uptime = time.time() - SERVICE_START_TIME
    
    return HealthStatus(
        status="healthy",
        version=settings.version,
        timestamp=datetime.utcnow(),
        uptime=uptime
    )


@router.get("/health/ready")
async def readiness_check():
    """Kubernetes readiness probe endpoint"""
    
    # Basic readiness checks
    try:
        # Check if we can import required modules
        import geopandas
        import shapely
        
        return {"status": "ready", "timestamp": datetime.utcnow()}
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")


@router.get("/health/live")
async def liveness_check():
    """Kubernetes liveness probe endpoint"""
    
    return {"status": "alive", "timestamp": datetime.utcnow()}


@router.get("/health/data-sources", response_model=DataSourceHealth)
async def data_sources_health():
    """Check health of external data sources"""
    
    try:
        # Test data source connections concurrently
        gee_task = asyncio.create_task(_test_google_earth_engine())
        osm_task = asyncio.create_task(_test_overpass_api())
        
        # Wait for both tests with timeout
        gee_healthy, osm_healthy = await asyncio.gather(
            gee_task, osm_task, return_exceptions=True
        )
        
        # Handle exceptions
        if isinstance(gee_healthy, Exception):
            gee_healthy = False
        if isinstance(osm_healthy, Exception):
            osm_healthy = False
        
        return DataSourceHealth(
            google_earth_engine=gee_healthy,
            overpass_api=osm_healthy,
            last_checked=datetime.utcnow()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Failed to check data source health: {str(e)}"
        )


async def _test_google_earth_engine() -> bool:
    """Test Google Earth Engine connectivity"""
    
    try:
        # Create a test connector
        config = DataSourceConfig(enabled=True, timeout=10)
        connector = GoogleEarthEngineConnector(DataSourceType.MICROSOFT_BUILDINGS, config)
        
        # Test connection
        return await connector.test_connection()
        
    except Exception:
        return False


async def _test_overpass_api() -> bool:
    """Test Overpass API connectivity"""
    
    try:
        # Create a test connector
        config = DataSourceConfig(enabled=True, timeout=10)
        connector = OpenStreetMapConnector(DataSourceType.OSM_BUILDINGS, config)
        
        # Test connection
        return await connector.test_connection()
        
    except Exception:
        return False


@router.get("/health/detailed")
async def detailed_health():
    """Detailed health check with system information"""
    
    try:
        import psutil
        import sys
        
        # Get system information
        memory_info = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        disk_info = psutil.disk_usage('/')
        
        # Get Python information
        python_version = sys.version
        
        # Get data source health
        data_sources = await data_sources_health()
        
        uptime = time.time() - SERVICE_START_TIME
        
        return {
            "service": {
                "status": "healthy",
                "version": settings.version,
                "uptime_seconds": uptime,
                "python_version": python_version
            },
            "system": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total_gb": round(memory_info.total / (1024**3), 2),
                    "available_gb": round(memory_info.available / (1024**3), 2),
                    "percent_used": memory_info.percent
                },
                "disk": {
                    "total_gb": round(disk_info.total / (1024**3), 2),
                    "free_gb": round(disk_info.free / (1024**3), 2),
                    "percent_used": round((disk_info.used / disk_info.total) * 100, 1)
                }
            },
            "data_sources": data_sources.dict(),
            "configuration": {
                "max_aoi_area_km2": settings.max_aoi_area_km2,
                "max_features_per_source": settings.max_features_per_source,
                "request_timeout": settings.request_timeout,
                "debug_mode": settings.debug
            },
            "timestamp": datetime.utcnow()
        }
        
    except ImportError:
        # psutil not available, return basic health
        return await health_check()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Detailed health check failed: {str(e)}"
        )