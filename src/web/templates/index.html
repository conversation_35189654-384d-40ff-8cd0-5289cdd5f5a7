<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    
    <!-- External Libraries -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/atlas.css') }}" />
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-satellite"></i>
                <h1>Atlas API</h1>
                <span class="tagline">Geospatial Data Extraction</span>
            </div>
            <nav class="nav">
                <a href="#" class="nav-link active" data-tab="extract">
                    <i class="fas fa-map"></i> Extract
                </a>
                <a href="#" class="nav-link" data-tab="docs">
                    <i class="fas fa-book"></i> Docs
                </a>
                <a href="/docs" class="nav-link">
                    <i class="fas fa-code"></i> API
                </a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Left Panel (1/3) -->
        <aside class="left-panel">
            <!-- AOI Definition Section -->
            <div class="panel-section">
                <h3><i class="fas fa-draw-polygon"></i> Define Area of Interest</h3>
                
                <!-- Drawing Tools -->
                <div class="tool-group">
                    <h4>Drawing Tools</h4>
                    <div class="button-group">
                        <button id="draw-polygon" class="btn btn-primary">
                            <i class="fas fa-draw-polygon"></i> Draw Polygon
                        </button>
                        <button id="clear-drawing" class="btn btn-secondary">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </div>
                </div>

                <!-- Coordinate Search -->
                <div class="tool-group">
                    <h4>Search by Coordinates</h4>
                    <div class="input-group">
                        <input type="text" id="lat-input" placeholder="Latitude" class="form-input">
                        <input type="text" id="lng-input" placeholder="Longitude" class="form-input">
                        <button id="search-coords" class="btn btn-secondary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- GeoJSON Upload -->
                <div class="tool-group">
                    <h4>Upload GeoJSON</h4>
                    <div class="upload-area" id="geojson-upload">
                        <i class="fas fa-upload"></i>
                        <p>Drop GeoJSON file or click to browse</p>
                        <input type="file" id="geojson-file" accept=".geojson,.json" hidden>
                    </div>
                </div>

                <!-- AOI Info -->
                <div class="tool-group">
                    <h4>Area Information</h4>
                    <div class="info-card">
                        <div class="info-item">
                            <span class="label">Area:</span>
                            <span id="aoi-area" class="value">Not defined</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Coordinates:</span>
                            <span id="aoi-coords" class="value">None</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Sources Section -->
            <div class="panel-section">
                <h3><i class="fas fa-database"></i> Data Sources</h3>
                
                <div class="source-list">
                    <div class="source-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="microsoft-buildings" checked>
                            <span class="checkmark"></span>
                            <span class="source-name">Microsoft Buildings</span>
                        </label>
                        <div class="source-config">
                            <input type="range" min="1" max="5" value="1" class="priority-slider" data-source="microsoft-buildings">
                            <span class="priority-label">Priority: 1</span>
                        </div>
                    </div>

                    <div class="source-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="google-buildings" checked>
                            <span class="checkmark"></span>
                            <span class="source-name">Google Buildings</span>
                        </label>
                        <div class="source-config">
                            <input type="range" min="1" max="5" value="2" class="priority-slider" data-source="google-buildings">
                            <span class="priority-label">Priority: 2</span>
                        </div>
                    </div>

                    <div class="source-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="osm-buildings" checked>
                            <span class="checkmark"></span>
                            <span class="source-name">OSM Buildings</span>
                        </label>
                        <div class="source-config">
                            <input type="range" min="1" max="5" value="3" class="priority-slider" data-source="osm-buildings">
                            <span class="priority-label">Priority: 3</span>
                        </div>
                    </div>

                    <div class="source-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="osm-roads" checked>
                            <span class="checkmark"></span>
                            <span class="source-name">OSM Roads</span>
                        </label>
                        <div class="source-config">
                            <input type="range" min="1" max="5" value="3" class="priority-slider" data-source="osm-roads">
                            <span class="priority-label">Priority: 3</span>
                        </div>
                    </div>

                    <div class="source-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="osm-railways">
                            <span class="checkmark"></span>
                            <span class="source-name">OSM Railways</span>
                        </label>
                        <div class="source-config">
                            <input type="range" min="1" max="5" value="3" class="priority-slider" data-source="osm-railways">
                            <span class="priority-label">Priority: 3</span>
                        </div>
                    </div>

                    <div class="source-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="osm-landmarks" checked>
                            <span class="checkmark"></span>
                            <span class="source-name">OSM Landmarks</span>
                        </label>
                        <div class="source-config">
                            <input type="range" min="1" max="5" value="3" class="priority-slider" data-source="osm-landmarks">
                            <span class="priority-label">Priority: 3</span>
                        </div>
                    </div>

                    <div class="source-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="osm-natural">
                            <span class="checkmark"></span>
                            <span class="source-name">OSM Natural</span>
                        </label>
                        <div class="source-config">
                            <input type="range" min="1" max="5" value="3" class="priority-slider" data-source="osm-natural">
                            <span class="priority-label">Priority: 3</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Options -->
            <div class="panel-section">
                <h3><i class="fas fa-cogs"></i> Processing Options</h3>
                
                <div class="options-grid">
                    <div class="option-group">
                        <label for="min-building-area">Min Building Area (m²)</label>
                        <input type="number" id="min-building-area" value="10" min="0" class="form-input">
                    </div>
                    
                    <div class="option-group">
                        <label for="simplification">Simplification Tolerance</label>
                        <input type="number" id="simplification" value="0.001" step="0.001" min="0" max="1" class="form-input">
                    </div>
                </div>
                
                <div class="option-group">
                    <label for="road-types">Road Types</label>
                    <select id="road-types" multiple class="form-select">
                        <option value="primary" selected>Primary</option>
                        <option value="secondary" selected>Secondary</option>
                        <option value="residential" selected>Residential</option>
                        <option value="motorway">Motorway</option>
                        <option value="trunk">Trunk</option>
                        <option value="tertiary">Tertiary</option>
                    </select>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="panel-section">
                <div class="action-buttons">
                    <button id="extract-btn" class="btn btn-primary btn-large" disabled>
                        <i class="fas fa-play"></i> Extract Features
                    </button>
                    <button id="export-btn" class="btn btn-success btn-large" disabled>
                        <i class="fas fa-download"></i> Export Results
                    </button>
                </div>
            </div>

            <!-- Results Summary -->
            <div class="panel-section" id="results-section" style="display: none;">
                <h3><i class="fas fa-chart-bar"></i> Results Summary</h3>
                <div id="results-summary" class="results-grid">
                    <!-- Populated by JavaScript -->
                </div>
            </div>
        </aside>

        <!-- Right Panel (2/3) - Map -->
        <div class="right-panel">
            <!-- Map Controls Overlay -->
            <div class="map-controls">
                <!-- Search Control -->
                <div class="control-group">
                    <div class="search-control">
                        <input type="text" id="map-search" placeholder="Search location..." class="search-input">
                        <button id="search-btn" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Map Type Control -->
                <div class="control-group">
                    <div class="map-type-control">
                        <select id="map-type" class="control-select">
                            <option value="streets">Streets</option>
                            <option value="satellite">Satellite</option>
                            <option value="terrain">Terrain</option>
                            <option value="dark">Dark</option>
                        </select>
                    </div>
                </div>

                <!-- Layer Control -->
                <div class="control-group">
                    <div class="layer-control">
                        <button id="layer-toggle" class="control-btn">
                            <i class="fas fa-layer-group"></i>
                        </button>
                        <div id="layer-panel" class="layer-panel hidden">
                            <h4>Data Layers</h4>
                            <div class="layer-list">
                                <!-- Populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Drawing Control -->
                <div class="control-group">
                    <div class="draw-control">
                        <button id="draw-toggle" class="control-btn">
                            <i class="fas fa-draw-polygon"></i>
                        </button>
                    </div>
                </div>

                <!-- Zoom Control -->
                <div class="control-group">
                    <div class="zoom-control">
                        <button id="zoom-in" class="control-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button id="zoom-out" class="control-btn">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Map Container -->
            <div id="map" class="map-container"></div>

            <!-- Loading Overlay -->
            <div id="loading-overlay" class="loading-overlay hidden">
                <div class="loading-content">
                    <div class="spinner"></div>
                    <h3>Processing Request</h3>
                    <p id="loading-message">Initializing extraction...</p>
                    <div class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Export Modal -->
    <div id="export-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-download"></i> Export Data</h3>
                <button id="modal-close" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <div class="export-options">
                    <h4>Select Export Format</h4>
                    <div class="format-grid">
                        <label class="format-option">
                            <input type="radio" name="export-format" value="geojson" checked>
                            <div class="format-card">
                                <i class="fas fa-code"></i>
                                <span>GeoJSON</span>
                                <small>Web standard format</small>
                            </div>
                        </label>
                        
                        <label class="format-option">
                            <input type="radio" name="export-format" value="kml">
                            <div class="format-card">
                                <i class="fas fa-globe"></i>
                                <span>KML</span>
                                <small>Google Earth format</small>
                            </div>
                        </label>
                        
                        <label class="format-option">
                            <input type="radio" name="export-format" value="shapefile">
                            <div class="format-card">
                                <i class="fas fa-map-marked-alt"></i>
                                <span>Shapefile</span>
                                <small>GIS standard format</small>
                            </div>
                        </label>
                        
                        <label class="format-option">
                            <input type="radio" name="export-format" value="csv">
                            <div class="format-card">
                                <i class="fas fa-table"></i>
                                <span>CSV</span>
                                <small>Spreadsheet format</small>
                            </div>
                        </label>
                        
                        <label class="format-option">
                            <input type="radio" name="export-format" value="dwg">
                            <div class="format-card">
                                <i class="fas fa-drafting-compass"></i>
                                <span>DWG</span>
                                <small>CAD format</small>
                            </div>
                        </label>
                        
                        <label class="format-option">
                            <input type="radio" name="export-format" value="kmz">
                            <div class="format-card">
                                <i class="fas fa-file-archive"></i>
                                <span>KMZ</span>
                                <small>Compressed KML</small>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="export-sources">
                    <h4>Select Data Sources</h4>
                    <div class="source-checkboxes">
                        <!-- Populated by JavaScript -->
                    </div>
                </div>

                <div class="export-metadata">
                    <label class="checkbox-label">
                        <input type="checkbox" id="include-metadata" checked>
                        <span class="checkmark"></span>
                        <span>Include processing metadata</span>
                    </label>
                </div>
            </div>
            
            <div class="modal-footer">
                <button id="cancel-export" class="btn btn-secondary">Cancel</button>
                <button id="confirm-export" class="btn btn-primary">
                    <i class="fas fa-download"></i> Export
                </button>
            </div>

            <!-- Export Progress -->
            <div id="export-progress" class="export-progress hidden">
                <div class="progress-content">
                    <div class="spinner"></div>
                    <h4>Preparing Export</h4>
                    <p id="export-message">Processing data...</p>
                    <div class="progress-bar">
                        <div id="export-progress-fill" class="progress-fill"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
    <script src="{{ url_for('static', path='/js/atlas.js') }}"></script>
</body>
</html>