<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/atlas.css') }}" />
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-satellite"></i>
                <h1>Atlas API Documentation</h1>
            </div>
            <nav class="nav">
                <a href="/web/" class="nav-link">
                    <i class="fas fa-map"></i> Interface
                </a>
                <a href="/docs" class="nav-link">
                    <i class="fas fa-code"></i> API Docs
                </a>
            </nav>
        </div>
    </header>

    <main class="main-container">
        <div class="docs-content">
            <h1>Atlas Geospatial API Documentation</h1>
            
            <section>
                <h2>Overview</h2>
                <p>The Atlas API provides automated extraction of geospatial data from multiple authoritative sources, designed specifically for telecom fiber network planning and infrastructure design.</p>
            </section>

            <section>
                <h2>Getting Started</h2>
                <ol>
                    <li>Navigate to the <a href="/web/">Web Interface</a></li>
                    <li>Draw your Area of Interest (AOI) on the map</li>
                    <li>Select your desired data sources</li>
                    <li>Click "Extract Features" to process</li>
                    <li>Export results in your preferred format</li>
                </ol>
            </section>

            <section>
                <h2>Data Sources</h2>
                <ul>
                    <li><strong>Microsoft Buildings:</strong> 1.4B+ building footprints worldwide</li>
                    <li><strong>Google Buildings:</strong> 1.8B+ AI-detected buildings</li>
                    <li><strong>OpenStreetMap:</strong> Global crowdsourced data (buildings, roads, landmarks)</li>
                </ul>
            </section>

            <section>
                <h2>API Reference</h2>
                <p>For detailed API documentation, visit the <a href="/docs">Interactive API Docs</a> or <a href="/redoc">ReDoc</a>.</p>
            </section>
        </div>
    </main>
</body>
</html>