"""
Web interface routes for Atlas API
"""

from fastapi import APIRouter, Request, Depends
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse
from pathlib import Path

# Setup templates
template_dir = Path(__file__).parent / "templates"
templates = Jinja2Templates(directory=str(template_dir))

router = APIRouter(prefix="/web", tags=["Web Interface"])


@router.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Main web interface"""
    return templates.TemplateResponse(
        "index.html",
        {
            "request": request,
            "title": "Atlas - Geospatial Data Extraction API",
            "api_base_url": "/api/v1"
        }
    )


@router.get("/docs", response_class=HTMLResponse)
async def web_docs(request: Request):
    """Web interface documentation"""
    return templates.TemplateResponse(
        "docs.html",
        {
            "request": request,
            "title": "Atlas API - Documentation",
            "api_base_url": "/api/v1"
        }
    )