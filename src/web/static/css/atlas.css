/* Atlas API Web Interface Styles */

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-medium: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: var(--spacing-lg) 0;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1.5" fill="white" opacity="0.1"/></svg>');
    pointer-events: none;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 100px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo i {
    font-size: 2rem;
    animation: pulse 2s infinite;
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
}

.tagline {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-left: var(--spacing-sm);
}

.nav {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* Main Container */
.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-2xl) 100px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-2xl);
    min-height: calc(100vh - 120px);
}

/* Left Panel */
.left-panel {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    height: fit-content;
    position: sticky;
    top: var(--spacing-xl);
    overflow-y: auto;
    max-height: calc(100vh - 160px);
}

.panel-section {
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.panel-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.panel-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
}

.panel-section h3 i {
    color: var(--primary-color);
}

/* Tool Groups */
.tool-group {
    margin-bottom: var(--spacing-lg);
}

.tool-group h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #475569;
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #047857;
    transform: translateY(-1px);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1rem;
    font-weight: 600;
}

.button-group {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* Form Elements */
.form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: var(--transition-fast);
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    cursor: pointer;
}

.input-group {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.input-group .form-input {
    flex: 1;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-fast);
    background: #fafbfc;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.02);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.upload-area i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.upload-area p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Info Card */
.info-card {
    background: #f8fafc;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item .label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.info-item .value {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

/* Data Sources */
.source-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.source-item {
    background: #f8fafc;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.source-item:hover {
    background: white;
    box-shadow: var(--shadow-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    margin-bottom: var(--spacing-sm);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.source-name {
    font-weight: 500;
    flex: 1;
}

.source-config {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.priority-slider {
    width: 80px;
}

.priority-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    min-width: 70px;
}

/* Options Grid */
.options-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.option-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Right Panel */
.right-panel {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

/* Map Container */
.map-container {
    width: 100%;
    height: calc(100vh - 200px);
    min-height: 600px;
    border-radius: var(--radius-xl);
    overflow: hidden;
}

/* Map Controls */
.map-controls {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.control-group {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.search-control {
    display: flex;
    padding: var(--spacing-sm);
}

.search-input {
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    flex: 1;
    font-size: 0.875rem;
    min-width: 200px;
}

.search-input:focus {
    outline: none;
}

.search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-btn:hover {
    background: var(--primary-dark);
}

.control-select {
    border: none;
    padding: var(--spacing-md);
    font-size: 0.875rem;
    background: white;
    cursor: pointer;
    min-width: 120px;
}

.control-btn {
    background: white;
    border: none;
    padding: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 1rem;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: var(--background-color);
    color: var(--primary-color);
}

.zoom-control {
    display: flex;
    flex-direction: column;
}

.zoom-control .control-btn {
    border-bottom: 1px solid var(--border-color);
}

.zoom-control .control-btn:last-child {
    border-bottom: none;
}

/* Layer Panel */
.layer-control {
    position: relative;
}

.layer-panel {
    position: absolute;
    top: 100%;
    right: 0;
    width: 250px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-sm);
    transform: translateY(-10px);
    opacity: 0;
    transition: var(--transition-medium);
    pointer-events: none;
}

.layer-panel:not(.hidden) {
    transform: translateY(0);
    opacity: 1;
    pointer-events: all;
}

.layer-panel h4 {
    margin-bottom: var(--spacing-md);
    font-size: 0.875rem;
    color: var(--text-primary);
}

.layer-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 250, 252, 0.95);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    border-radius: var(--radius-xl);
}

.loading-content {
    text-align: center;
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

.loading-content h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.loading-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
    width: 0%;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    opacity: 0;
    transition: var(--transition-medium);
    pointer-events: none;
}

.modal:not(.hidden) {
    opacity: 1;
    pointer-events: all;
}

.modal-content {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: translateY(20px);
    transition: var(--transition-medium);
}

.modal:not(.hidden) .modal-content {
    transform: translateY(0);
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: var(--background-color);
}

.modal-body {
    padding: var(--spacing-xl);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* Export Options */
.export-options {
    margin-bottom: var(--spacing-xl);
}

.export-options h4 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.format-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.format-option {
    cursor: pointer;
}

.format-option input[type="radio"] {
    display: none;
}

.format-card {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-fast);
    background: white;
}

.format-option input[type="radio"]:checked + .format-card {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.format-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.format-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.format-card span {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.format-card small {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.export-sources {
    margin-bottom: var(--spacing-xl);
}

.export-sources h4 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.source-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
}

.export-metadata {
    margin-bottom: var(--spacing-lg);
}

.export-progress {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-xl);
}

.progress-content {
    text-align: center;
    padding: var(--spacing-2xl);
}

.progress-content h4 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.progress-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

/* Results */
.results-grid {
    display: grid;
    gap: var(--spacing-md);
}

.result-item {
    background: #f8fafc;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.result-name {
    font-weight: 600;
    color: var(--text-primary);
}

.result-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.result-status.completed {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.result-status.failed {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.result-stats {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Utilities */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mt-auto {
    margin-top: auto;
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-container {
        padding: var(--spacing-xl) 50px;
    }
    
    .header-content {
        padding: 0 50px;
    }
}

@media (max-width: 768px) {
    .main-container {
        grid-template-columns: 1fr;
        padding: var(--spacing-lg) var(--spacing-lg);
        gap: var(--spacing-lg);
    }
    
    .left-panel {
        position: static;
        max-height: none;
        order: 2;
    }
    
    .right-panel {
        order: 1;
        min-height: 400px;
    }
    
    .map-container {
        height: 400px;
    }
    
    .header-content {
        padding: 0 var(--spacing-lg);
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .map-controls {
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .control-group {
        flex: 1;
        min-width: 120px;
    }
    
    .search-control {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .search-input {
        min-width: 100px;
    }
    
    .options-grid {
        grid-template-columns: 1fr;
    }
    
    .format-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .source-checkboxes {
        grid-template-columns: 1fr;
    }
    
    .button-group {
        justify-content: center;
    }
    
    .action-buttons {
        position: sticky;
        bottom: 0;
        background: white;
        padding: var(--spacing-lg);
        margin: 0 calc(var(--spacing-xl) * -1);
        border-top: 1px solid var(--border-color);
    }
}

@media (max-width: 480px) {
    .main-container {
        padding: var(--spacing-md);
    }
    
    .header-content {
        padding: 0 var(--spacing-md);
    }
    
    .left-panel {
        padding: var(--spacing-lg);
    }
    
    .panel-section {
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-lg);
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .tagline {
        display: none;
    }
    
    .nav-link {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: 0.8rem;
    }
    
    .map-controls {
        position: relative;
        top: 0;
        right: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.95);
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }
    
    .map-container {
        height: 300px;
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    }
}

/* Print Styles */
@media print {
    .header,
    .map-controls,
    .modal {
        display: none !important;
    }
    
    .main-container {
        grid-template-columns: 1fr;
        padding: 0;
    }
    
    .map-container {
        height: 400px;
    }
}