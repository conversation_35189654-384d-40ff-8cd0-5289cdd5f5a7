/**
 * Atlas API Web Interface JavaScript
 */

class AtlasInterface {
    constructor() {
        this.map = null;
        this.drawingLayer = null;
        this.currentPolygon = null;
        this.dataLayers = new Map();
        this.currentJobId = null;
        this.apiBaseUrl = '/api/v1';
        
        this.init();
    }

    init() {
        this.initMap();
        this.setupEventListeners();
        this.setupDrawingControls();
    }

    initMap() {
        this.map = L.map('map', { zoomControl: false }).setView([-1.2921, 36.8219], 13);
        this.addTileLayer('streets');
        this.drawingLayer = L.layerGroup().addTo(this.map);
    }

    setupEventListeners() {
        // Map controls
        document.getElementById('map-type').addEventListener('change', (e) => {
            this.changeMapType(e.target.value);
        });

        document.getElementById('zoom-in').addEventListener('click', () => this.map.zoomIn());
        document.getElementById('zoom-out').addEventListener('click', () => this.map.zoomOut());

        // Search
        document.getElementById('search-btn').addEventListener('click', () => this.searchLocation());
        document.getElementById('search-coords').addEventListener('click', () => this.searchByCoordinates());

        // Drawing
        document.getElementById('draw-polygon').addEventListener('click', () => this.enablePolygonDrawing());
        document.getElementById('clear-drawing').addEventListener('click', () => this.clearDrawing());

        // File upload
        this.setupFileUpload();

        // Actions
        document.getElementById('extract-btn').addEventListener('click', () => this.extractFeatures());
        document.getElementById('export-btn').addEventListener('click', () => this.showExportModal());

        // Modal
        document.getElementById('modal-close').addEventListener('click', () => this.hideExportModal());
        document.getElementById('cancel-export').addEventListener('click', () => this.hideExportModal());
        document.getElementById('confirm-export').addEventListener('click', () => this.performExport());

        // Layer toggle
        document.getElementById('layer-toggle').addEventListener('click', () => this.toggleLayerPanel());
    }

    setupDrawingControls() {
        const drawControl = new L.Control.Draw({
            draw: {
                polygon: {
                    shapeOptions: { color: '#2563eb', weight: 3, fillOpacity: 0.1 }
                },
                polyline: false, rectangle: false, circle: false, marker: false, circlemarker: false
            },
            edit: { featureGroup: this.drawingLayer, remove: true }
        });

        this.map.on(L.Draw.Event.CREATED, (e) => this.onPolygonDrawn(e.layer));
        this.map.on(L.Draw.Event.DELETED, () => this.onPolygonDeleted());
    }

    setupFileUpload() {
        const uploadArea = document.getElementById('geojson-upload');
        const fileInput = document.getElementById('geojson-file');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            if (e.dataTransfer.files.length > 0) {
                this.handleFileUpload(e.dataTransfer.files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileUpload(e.target.files[0]);
            }
        });
    }

    async handleFileUpload(file) {
        try {
            const text = await file.text();
            const geojson = JSON.parse(text);
            
            let geometry;
            if (geojson.type === 'FeatureCollection' && geojson.features.length > 0) {
                geometry = geojson.features[0].geometry;
            } else if (geojson.type === 'Polygon') {
                geometry = geojson;
            }
            
            if (geometry && geometry.type === 'Polygon') {
                this.loadPolygonFromGeoJSON(geometry);
                this.showNotification('GeoJSON loaded successfully', 'success');
            } else {
                this.showNotification('Please upload a polygon geometry', 'error');
            }
        } catch (error) {
            this.showNotification('Error parsing GeoJSON file', 'error');
        }
    }

    loadPolygonFromGeoJSON(geometry) {
        this.clearDrawing();
        const coordinates = geometry.coordinates[0].map(coord => [coord[1], coord[0]]);
        const polygon = L.polygon(coordinates, { color: '#2563eb', weight: 3, fillOpacity: 0.1 });
        this.drawingLayer.addLayer(polygon);
        this.currentPolygon = polygon;
        this.map.fitBounds(polygon.getBounds());
        this.updateAOIInfo();
        this.enableExtractButton();
    }

    changeMapType(type) {
        this.map.eachLayer((layer) => {
            if (layer instanceof L.TileLayer) this.map.removeLayer(layer);
        });
        this.addTileLayer(type);
    }

    addTileLayer(type) {
        const layers = {
            streets: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            satellite: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            terrain: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
            dark: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
        };
        
        L.tileLayer(layers[type] || layers.streets).addTo(this.map);
    }

    async searchLocation() {
        const query = document.getElementById('map-search').value.trim();
        if (!query) return;

        try {
            const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1`);
            const results = await response.json();

            if (results.length > 0) {
                const result = results[0];
                this.map.setView([parseFloat(result.lat), parseFloat(result.lon)], 15);
                this.showNotification('Location found', 'success');
            } else {
                this.showNotification('Location not found', 'error');
            }
        } catch (error) {
            this.showNotification('Search error', 'error');
        }
    }

    searchByCoordinates() {
        const lat = parseFloat(document.getElementById('lat-input').value);
        const lng = parseFloat(document.getElementById('lng-input').value);

        if (!this.validateCoordinates(lat, lng)) {
            this.showNotification('Please enter valid coordinates (-90 to 90 for latitude, -180 to 180 for longitude)', 'error');
            return;
        }

        this.map.setView([lat, lng], 15);
        
        // Add a temporary marker
        const marker = L.marker([lat, lng]).addTo(this.map)
            .bindPopup(`Coordinates: ${lat.toFixed(4)}, ${lng.toFixed(4)}`)
            .openPopup();
        
        // Remove marker after 5 seconds
        setTimeout(() => {
            this.map.removeLayer(marker);
        }, 5000);
        
        this.showNotification('Location found', 'success');
    }

    enablePolygonDrawing() {
        this.showNotification('Click on the map to start drawing', 'info');
    }

    clearDrawing() {
        this.drawingLayer.clearLayers();
        this.currentPolygon = null;
        this.updateAOIInfo();
        this.disableExtractButton();
    }

    onPolygonDrawn(layer) {
        this.clearDrawing();
        this.drawingLayer.addLayer(layer);
        this.currentPolygon = layer;
        this.updateAOIInfo();
        this.enableExtractButton();
    }

    onPolygonDeleted() {
        this.currentPolygon = null;
        this.updateAOIInfo();
        this.disableExtractButton();
    }

    updateAOIInfo() {
        const areaElement = document.getElementById('aoi-area');
        const coordsElement = document.getElementById('aoi-coords');

        if (this.currentPolygon) {
            const bounds = this.currentPolygon.getBounds();
            const area = this.calculateArea(this.currentPolygon);
            areaElement.textContent = this.formatArea(area);
            coordsElement.textContent = `${bounds.getNorth().toFixed(4)}, ${bounds.getWest().toFixed(4)} to ${bounds.getSouth().toFixed(4)}, ${bounds.getEast().toFixed(4)}`;
        } else {
            areaElement.textContent = 'Not defined';
            coordsElement.textContent = 'None';
        }
    }

    calculateArea(polygon) {
        const latLngs = polygon.getLatLngs()[0];
        let area = 0;
        
        for (let i = 0; i < latLngs.length; i++) {
            const j = (i + 1) % latLngs.length;
            area += latLngs[i].lng * latLngs[j].lat;
            area -= latLngs[j].lng * latLngs[i].lat;
        }
        
        return Math.abs(area) / 2 * 111320 * 111320;
    }

    enableExtractButton() {
        document.getElementById('extract-btn').disabled = false;
    }

    disableExtractButton() {
        document.getElementById('extract-btn').disabled = true;
    }

    async extractFeatures() {
        if (!this.currentPolygon) {
            this.showNotification('Please draw a polygon first', 'error');
            return;
        }

        const requestData = this.buildExtractRequest();
        this.showLoadingOverlay('Extracting features...');
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/extract-features`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();
            this.currentJobId = result.job_id;
            
            if (result.status === 'completed') {
                this.displayResults(result);
                this.hideLoadingOverlay();
                this.showNotification('Features extracted successfully!', 'success');
                document.getElementById('export-btn').disabled = false;
            } else {
                this.pollJobStatus(result.job_id);
            }
        } catch (error) {
            this.hideLoadingOverlay();
            this.showNotification('Extraction failed', 'error');
        }
    }

    buildExtractRequest() {
        const coordinates = this.currentPolygon.getLatLngs()[0].map(latLng => [latLng.lng, latLng.lat]);
        coordinates.push(coordinates[0]);

        const dataSources = {};
        document.querySelectorAll('.source-item').forEach(item => {
            const checkbox = item.querySelector('input[type="checkbox"]');
            const slider = item.querySelector('.priority-slider');
            const sourceKey = checkbox.id.replace('-', '_');
            
            dataSources[sourceKey] = {
                enabled: checkbox.checked,
                priority: parseInt(slider.value),
                timeout: 30
            };
        });

        return {
            aoi_boundary: { type: "Polygon", coordinates: [coordinates] },
            data_sources: dataSources,
            filters: {
                min_building_area: parseFloat(document.getElementById('min-building-area').value) || null,
                simplification_tolerance: parseFloat(document.getElementById('simplification').value) || 0.001
            }
        };
    }

    async pollJobStatus(jobId) {
        const poll = async () => {
            try {
                const response = await fetch(`${this.apiBaseUrl}/job-status/${jobId}`);
                const status = await response.json();

                if (status.status === 'completed') {
                    const resultResponse = await fetch(`${this.apiBaseUrl}/results/${jobId}`);
                    const result = await resultResponse.json();
                    this.displayResults(result);
                    this.hideLoadingOverlay();
                    this.showNotification('Extraction completed!', 'success');
                    document.getElementById('export-btn').disabled = false;
                } else if (status.status === 'failed') {
                    this.hideLoadingOverlay();
                    this.showNotification('Extraction failed', 'error');
                } else {
                    setTimeout(poll, 2000);
                }
            } catch (error) {
                this.hideLoadingOverlay();
                this.showNotification('Status check failed', 'error');
            }
        };

        poll();
    }

    displayResults(result) {
        document.getElementById('results-section').style.display = 'block';
        
        this.dataLayers.forEach(layer => this.map.removeLayer(layer));
        this.dataLayers.clear();

        const summaryContainer = document.getElementById('results-summary');
        summaryContainer.innerHTML = '';

        Object.entries(result.results).forEach(([sourceKey, sourceResult]) => {
            if (sourceResult.status === 'completed' && sourceResult.geojson.features.length > 0) {
                const layer = L.geoJSON(sourceResult.geojson, {
                    style: this.getLayerStyle(sourceKey)
                });
                
                this.dataLayers.set(sourceKey, layer);
                layer.addTo(this.map);

                const resultItem = this.createResultItem(sourceKey, sourceResult);
                summaryContainer.appendChild(resultItem);
            }
        });

        this.updateLayerPanel();
    }

    getLayerStyle(sourceKey) {
        const styles = {
            microsoft_buildings: { color: '#dc2626', weight: 1, fillOpacity: 0.3 },
            google_buildings: { color: '#ea580c', weight: 1, fillOpacity: 0.3 },
            osm_buildings: { color: '#ca8a04', weight: 1, fillOpacity: 0.3 },
            osm_roads: { color: '#2563eb', weight: 2, fillOpacity: 0 },
            osm_railways: { color: '#059669', weight: 3, fillOpacity: 0 },
            osm_landmarks: { color: '#7c3aed', weight: 1, fillOpacity: 0.5 }
        };
        return styles[sourceKey] || { color: '#64748b', weight: 1, fillOpacity: 0.3 };
    }

    createResultItem(sourceKey, sourceResult) {
        const item = document.createElement('div');
        item.className = 'result-item';
        const sourceName = sourceKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        
        item.innerHTML = `
            <div class="result-header">
                <span class="result-name">${sourceName}</span>
                <span class="result-status ${sourceResult.status}">${sourceResult.status}</span>
            </div>
            <div class="result-stats">${sourceResult.stats.count} features</div>
        `;
        return item;
    }

    updateLayerPanel() {
        const layerList = document.querySelector('.layer-list');
        layerList.innerHTML = '';

        this.dataLayers.forEach((layer, sourceKey) => {
            const sourceName = sourceKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            
            const layerItem = document.createElement('label');
            layerItem.className = 'checkbox-label';
            layerItem.innerHTML = `
                <input type="checkbox" data-layer="${sourceKey}" checked>
                <span class="checkmark"></span>
                <span>${sourceName}</span>
            `;

            layerItem.querySelector('input').addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.map.addLayer(layer);
                } else {
                    this.map.removeLayer(layer);
                }
            });

            layerList.appendChild(layerItem);
        });
    }

    toggleLayerPanel() {
        document.getElementById('layer-panel').classList.toggle('hidden');
    }

    showExportModal() {
        if (!this.currentJobId) {
            this.showNotification('No data to export', 'error');
            return;
        }

        const sourceCheckboxes = document.querySelector('.source-checkboxes');
        sourceCheckboxes.innerHTML = '';

        this.dataLayers.forEach((layer, sourceKey) => {
            const sourceName = sourceKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            const checkboxItem = document.createElement('label');
            checkboxItem.className = 'checkbox-label';
            checkboxItem.innerHTML = `
                <input type="checkbox" value="${sourceKey}" checked>
                <span class="checkmark"></span>
                <span>${sourceName}</span>
            `;
            sourceCheckboxes.appendChild(checkboxItem);
        });

        document.getElementById('export-modal').classList.remove('hidden');
    }

    hideExportModal() {
        document.getElementById('export-modal').classList.add('hidden');
    }

    async performExport() {
        const format = document.querySelector('input[name="export-format"]:checked').value;
        const selectedSources = Array.from(document.querySelectorAll('.source-checkboxes input:checked')).map(cb => cb.value);

        try {
            const response = await fetch(`${this.apiBaseUrl}/export/${this.currentJobId}/${format}`);
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `atlas_export_${this.currentJobId}.${format}`;
            a.click();
            window.URL.revokeObjectURL(url);

            this.hideExportModal();
            this.showNotification('Export downloaded!', 'success');
        } catch (error) {
            this.showNotification('Export failed', 'error');
        }
    }

    showLoadingOverlay(message) {
        document.getElementById('loading-message').textContent = message;
        document.getElementById('loading-overlay').classList.remove('hidden');
    }

    hideLoadingOverlay() {
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `atlas-notification atlas-notification-${type}`;
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 4000;
            background: ${type === 'success' ? '#059669' : type === 'error' ? '#dc2626' : '#2563eb'};
            color: white; padding: 1rem 1.5rem; border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1); cursor: pointer;
            transform: translateX(100%); transition: transform 0.3s ease;
            max-width: 300px; word-wrap: break-word;
        `;
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        notification.onclick = () => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        };
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    /**
     * Utility function to validate coordinates
     */
    validateCoordinates(lat, lng) {
        return !isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
    }

    /**
     * Utility function to format area display
     */
    formatArea(areaM2) {
        if (areaM2 < 1000000) {
            return `${(areaM2 / 10000).toFixed(2)} hectares`;
        } else {
            return `${(areaM2 / 1000000).toFixed(2)} km²`;
        }
    }

    /**
     * Utility function to debounce function calls
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize the interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AtlasInterface();
});