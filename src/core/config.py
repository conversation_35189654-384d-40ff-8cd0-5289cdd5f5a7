"""
Application configuration management using Pydantic Settings
"""

import os
import json
import base64
import tempfile
from functools import lru_cache
from typing import List, Optional, Union
from pathlib import Path

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application Info
    service_name: str = Field(default="atlas-geospatial-api", description="Service name")
    version: str = Field(default="1.0.0", description="API version")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    
    # CORS settings
    allowed_origins: Union[str, List[str]] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="Allowed CORS origins"
    )
    
    @field_validator('allowed_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v) -> List[str]:
        """Parse CORS origins from string or list"""
        if isinstance(v, str):
            # Handle comma-separated string from environment variables
            if v.strip():
                return [origin.strip() for origin in v.split(',')]
            else:
                return ["*"]  # Default to allow all if empty
        elif isinstance(v, list):
            return v
        else:
            # Fallback for any other type
            return [str(v)] if v else ["*"]
    
    # Google Earth Engine Configuration - Method 1: Local file
    google_application_credentials: Optional[str] = Field(
        default=None,
        description="Path to Google Earth Engine service account JSON"
    )
    
    # Google Earth Engine Configuration - Method 2: Individual environment variables
    google_cloud_project: Optional[str] = Field(
        default=None,
        description="Google Cloud Project ID"
    )
    google_service_account_email: Optional[str] = Field(
        default=None,
        description="Google service account email"
    )
    google_private_key: Optional[str] = Field(
        default=None,
        description="Google service account private key"
    )
    google_private_key_id: Optional[str] = Field(
        default=None,
        description="Google service account private key ID"
    )
    google_client_id: Optional[str] = Field(
        default=None,
        description="Google service account client ID"
    )
    
    # Processing Constraints (essential only)
    max_aoi_area_km2: float = Field(
        default=100.0,
        description="Maximum AOI area in square kilometers"
    )
    request_timeout: int = Field(
        default=300,
        description="Request timeout in seconds"
    )
    
    # OpenStreetMap Configuration
    overpass_api_url: str = Field(
        default="https://overpass-api.de/api/interpreter",
        description="Overpass API endpoint URL"
    )
    overpass_rate_limit: float = Field(
        default=0.5,
        description="Overpass API rate limit (requests per second)"
    )
    overpass_timeout: int = Field(
        default=25,
        description="Overpass API request timeout in seconds"
    )
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        env_ignore_empty=True
    )
    
    def get_google_credentials_path(self) -> Optional[str]:
        """
        Get Google Earth Engine credentials using multiple methods with priority:
        1. Local JSON file (development)
        2. Individual environment variables (secure cloud deployment)
        3. Base64 encoded key (legacy cloud deployment)
        """
        # Method 1: Local JSON file (highest priority for development)
        if self.google_application_credentials and Path(self.google_application_credentials).exists():
            print(f"✓ Using local credentials file: {self.google_application_credentials}")
            return self.google_application_credentials
        
        # Method 2: Individual environment variables (secure cloud deployment)
        if all([
            self.google_cloud_project,
            self.google_service_account_email,
            self.google_private_key,
            self.google_private_key_id,
            self.google_client_id
        ]):
            try:
                # Create service account JSON from individual components
                service_account_data = {
                    "type": "service_account",
                    "project_id": self.google_cloud_project,
                    "private_key_id": self.google_private_key_id,
                    "private_key": self.google_private_key.replace('\\n', '\n'),  # Handle escaped newlines
                    "client_email": self.google_service_account_email,
                    "client_id": self.google_client_id,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                    "client_x509_cert_url": f"https://www.googleapis.com/robot/v1/metadata/x509/{self.google_service_account_email.replace('@', '%40')}",
                    "universe_domain": "googleapis.com"
                }
                
                # Create temporary file
                temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
                json.dump(service_account_data, temp_file, indent=2)
                temp_file.close()
                
                # Set environment variable for Google libraries
                os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = temp_file.name
                print(f"✓ Using individual environment variables for authentication")
                return temp_file.name
                
            except Exception as e:
                print(f"Error creating credentials from environment variables: {e}")
        
        print("❌ No valid Google Earth Engine credentials found")
        print("   Options:")
        print("   1. Place JSON file at: ./keys/gee-service-account.json")
        print("   2. Set individual environment variables (recommended for cloud)")
        return None


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()