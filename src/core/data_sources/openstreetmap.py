"""
OpenStreetMap connector using Overpass API and OSMnx
"""

import asyncio
import time
from typing import Dict, Optional, Any, List
import logging

import aiohttp
import osmnx as ox
import geopandas as gpd
from geojson_pydantic import FeatureCollection
from shapely.geometry import Polygon
import overpy

from src.core.data_sources.base import BaseDataSource
from src.api.schemas import DataSourceType, DataSourceConfig
from src.utils.exceptions import DataSourceError, RateLimitError, TimeoutError
from src.utils.geometry import create_bounding_box
from src.core.config import get_settings

logger = logging.getLogger(__name__)

# Configure OSMnx using the new settings module
ox.settings.use_cache = True
ox.settings.log_console = False


class OpenStreetMapConnector(BaseDataSource):
    """Connector for OpenStreetMap data via Overpass API"""
    
    def __init__(self, source_type: DataSourceType, config: DataSourceConfig):
        super().__init__(source_type, config)
        self.settings = get_settings()
        self.overpass_api = overpy.Overpass()
        self._last_request_time = 0
        
        # Rate limiting
        self.min_request_interval = 1.0 / self.settings.overpass_rate_limit  # seconds between requests
    
    async def test_connection(self) -> bool:
        """Test Overpass API connection"""
        try:
            # Simple test query
            test_query = """
            [out:json][timeout:5];
            (
              node["amenity"="hospital"](51.5,-0.13,51.51,-0.12);
            );
            out 1;
            """
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.settings.overpass_api_url,
                    data=test_query,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            self.logger.error(f"Overpass API connection test failed: {e}")
            return False
    
    async def extract_features(self, aoi_polygon: Polygon, filters: Optional[Dict[str, Any]] = None) -> FeatureCollection:
        """Extract features from OpenStreetMap"""
        
        try:
            # Apply rate limiting
            await self._apply_rate_limit()
            
            # Extract features based on source type
            if self.source_type == DataSourceType.OSM_BUILDINGS:
                return await self._extract_buildings(aoi_polygon, filters)
            elif self.source_type == DataSourceType.OSM_ROADS:
                return await self._extract_roads(aoi_polygon, filters)
            elif self.source_type == DataSourceType.OSM_RAILWAYS:
                return await self._extract_railways(aoi_polygon, filters)
            elif self.source_type == DataSourceType.OSM_LANDMARKS:
                return await self._extract_landmarks(aoi_polygon, filters)
            elif self.source_type == DataSourceType.OSM_NATURAL:
                return await self._extract_natural_features(aoi_polygon, filters)
            else:
                raise DataSourceError(
                    self.source_type.value,
                    f"Unsupported OSM source type: {self.source_type.value}"
                )
                
        except Exception as e:
            self.logger.error(f"Failed to extract {self.source_type.value}: {e}")
            raise DataSourceError(self.source_type.value, str(e))
    
    async def _apply_rate_limit(self):
        """Apply rate limiting for Overpass API"""
        current_time = time.time()
        time_since_last_request = current_time - self._last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)
        
        self._last_request_time = time.time()
    
    async def _extract_buildings(self, aoi_polygon: Polygon, filters: Optional[Dict[str, Any]]) -> FeatureCollection:
        """Extract building features from OSM"""
        
        try:
            # Use OSMnx for building extraction (more reliable than direct Overpass)
            buildings_gdf = ox.geometries_from_polygon(
                aoi_polygon,
                tags={'building': True}
            )
            
            # Apply filters if provided
            if filters:
                if filters.get('min_building_area'):
                    min_area = filters['min_building_area']
                    # Calculate areas (rough approximation)
                    areas = buildings_gdf.geometry.area
                    buildings_gdf = buildings_gdf[areas >= min_area * 1e-10]  # Convert from m² to degrees²
            
            # Convert to GeoJSON format
            if not buildings_gdf.empty:
                # Reset index to avoid issues with conversion
                buildings_gdf = buildings_gdf.reset_index()
                
                # Convert to GeoJSON
                geojson_str = buildings_gdf.to_json()
                import json
                geojson_data = json.loads(geojson_str)
                
                return FeatureCollection(**geojson_data)
            else:
                return FeatureCollection(type="FeatureCollection", features=[])
                
        except Exception as e:
            self.logger.warning(f"OSMnx extraction failed, trying Overpass API: {e}")
            return await self._extract_buildings_overpass(aoi_polygon, filters)
    
    async def _extract_buildings_overpass(self, aoi_polygon: Polygon, filters: Optional[Dict[str, Any]]) -> FeatureCollection:
        """Extract buildings using direct Overpass API query"""
        
        bbox = create_bounding_box(aoi_polygon)
        bbox_str = f"{bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]}"  # south,west,north,east
        
        query = f"""
        [out:json][timeout:{self.settings.overpass_timeout}];
        (
          way["building"]({bbox_str});
          relation["building"]({bbox_str});
        );
        out geom;
        """
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.settings.overpass_api_url,
                    data=query,
                    timeout=aiohttp.ClientTimeout(total=self.settings.overpass_timeout + 5)
                ) as response:
                    if response.status == 429:
                        raise RateLimitError("overpass_api")
                    
                    response.raise_for_status()
                    data = await response.json()
                    
                    return self._convert_overpass_to_geojson(data)
                    
        except asyncio.TimeoutError:
            raise TimeoutError("overpass_buildings_query", self.settings.overpass_timeout)
        except Exception as e:
            raise DataSourceError("osm_buildings", f"Overpass query failed: {str(e)}")
    
    async def _extract_roads(self, aoi_polygon: Polygon, filters: Optional[Dict[str, Any]]) -> FeatureCollection:
        """Extract road features from OSM"""
        
        try:
            # Define road types to extract
            road_types = ['motorway', 'trunk', 'primary', 'secondary', 'tertiary', 'residential']
            
            if filters and filters.get('road_types'):
                road_types = filters['road_types']
            
            # Use OSMnx to get road network
            graph = ox.graph_from_polygon(aoi_polygon, network_type='all')
            edges_gdf = ox.graph_to_gdfs(graph, nodes=False, edges=True)
            
            # Filter by road types if specified
            if 'highway' in edges_gdf.columns:
                if isinstance(edges_gdf['highway'].iloc[0], list):
                    # Handle cases where highway is a list
                    mask = edges_gdf['highway'].apply(
                        lambda x: any(road_type in x if isinstance(x, list) else road_type == x for road_type in road_types)
                    )
                else:
                    mask = edges_gdf['highway'].isin(road_types)
                
                edges_gdf = edges_gdf[mask]
            
            # Convert to GeoJSON
            if not edges_gdf.empty:
                edges_gdf = edges_gdf.reset_index()
                geojson_str = edges_gdf.to_json()
                import json
                geojson_data = json.loads(geojson_str)
                
                return FeatureCollection(**geojson_data)
            else:
                return FeatureCollection(type="FeatureCollection", features=[])
                
        except Exception as e:
            self.logger.error(f"Failed to extract roads: {e}")
            raise DataSourceError("osm_roads", str(e))
    
    async def _extract_railways(self, aoi_polygon: Polygon, filters: Optional[Dict[str, Any]]) -> FeatureCollection:
        """Extract railway features from OSM"""
        
        try:
            railways_gdf = ox.geometries_from_polygon(
                aoi_polygon,
                tags={'railway': True}
            )
            
            # Convert to GeoJSON
            if not railways_gdf.empty:
                railways_gdf = railways_gdf.reset_index()
                geojson_str = railways_gdf.to_json()
                import json
                geojson_data = json.loads(geojson_str)
                
                return FeatureCollection(**geojson_data)
            else:
                return FeatureCollection(type="FeatureCollection", features=[])
                
        except Exception as e:
            self.logger.error(f"Failed to extract railways: {e}")
            raise DataSourceError("osm_railways", str(e))
    
    async def _extract_landmarks(self, aoi_polygon: Polygon, filters: Optional[Dict[str, Any]]) -> FeatureCollection:
        """Extract landmark features from OSM"""
        
        try:
            # Define landmark tags
            landmark_tags = {
                'amenity': True,
                'tourism': True,
                'shop': True,
                'leisure': True
            }
            
            if filters and filters.get('landmark_types'):
                # Filter by specific landmark types
                landmark_types = filters['landmark_types']
                landmark_tags = {tag: True for tag in landmark_types if tag in landmark_tags}
            
            landmarks_gdf = ox.geometries_from_polygon(aoi_polygon, tags=landmark_tags)
            
            # Convert to GeoJSON
            if not landmarks_gdf.empty:
                landmarks_gdf = landmarks_gdf.reset_index()
                geojson_str = landmarks_gdf.to_json()
                import json
                geojson_data = json.loads(geojson_str)
                
                return FeatureCollection(**geojson_data)
            else:
                return FeatureCollection(type="FeatureCollection", features=[])
                
        except Exception as e:
            self.logger.error(f"Failed to extract landmarks: {e}")
            raise DataSourceError("osm_landmarks", str(e))
    
    async def _extract_natural_features(self, aoi_polygon: Polygon, filters: Optional[Dict[str, Any]]) -> FeatureCollection:
        """Extract natural features from OSM"""
        
        try:
            natural_tags = {
                'natural': ['water', 'forest', 'wood', 'park'],
                'landuse': ['forest', 'grass', 'meadow'],
                'leisure': ['park', 'garden', 'nature_reserve']
            }
            
            natural_gdf = ox.geometries_from_polygon(aoi_polygon, tags=natural_tags)
            
            # Convert to GeoJSON
            if not natural_gdf.empty:
                natural_gdf = natural_gdf.reset_index()
                geojson_str = natural_gdf.to_json()
                import json
                geojson_data = json.loads(geojson_str)
                
                return FeatureCollection(**geojson_data)
            else:
                return FeatureCollection(type="FeatureCollection", features=[])
                
        except Exception as e:
            self.logger.error(f"Failed to extract natural features: {e}")
            raise DataSourceError("osm_natural", str(e))
    
    def _convert_overpass_to_geojson(self, overpass_data: Dict) -> FeatureCollection:
        """Convert Overpass API response to GeoJSON FeatureCollection"""
        
        features = []
        
        try:
            for element in overpass_data.get('elements', []):
                if element.get('type') == 'way' and 'geometry' in element:
                    # Convert way to GeoJSON feature
                    coords = [[node['lon'], node['lat']] for node in element['geometry']]
                    
                    # Determine if it's a polygon (closed way) or linestring
                    if len(coords) > 2 and coords[0] == coords[-1]:
                        geometry = {
                            "type": "Polygon",
                            "coordinates": [coords]
                        }
                    else:
                        geometry = {
                            "type": "LineString",
                            "coordinates": coords
                        }
                    
                    feature = {
                        "type": "Feature",
                        "id": element.get('id'),
                        "geometry": geometry,
                        "properties": element.get('tags', {})
                    }
                    
                    features.append(feature)
            
            return FeatureCollection(
                type="FeatureCollection",
                features=features
            )
            
        except Exception as e:
            self.logger.error(f"Failed to convert Overpass data to GeoJSON: {e}")
            return FeatureCollection(type="FeatureCollection", features=[])