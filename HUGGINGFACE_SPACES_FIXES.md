# HuggingFace Spaces Deployment Fixes

## ✅ Fixed Issues

### 1. Pydantic Validation Error - RESOLVED ✅
**Problem**: Environment variables with trailing newlines causing validation errors
**Solution**: Added field validators to strip whitespace from all environment variables

### 2. Web UI Static Files - RESOLVED ✅  
**Problem**: CSS/JS files not loading due to incorrect `url_for` usage
**Solution**: Added proper `url_for` function to Jinja2 templates and improved static file path detection

## 🔧 Remaining Issue: Google Earth Engine Authentication

### Problem
OAuth scope error: "Invalid OAuth scope or ID token audience provided"

### Root Cause
The service account lacks proper permissions for Google Earth Engine access.

### Solution Steps

#### Step 1: Add Earth Engine Role to Service Account
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **IAM & Admin > IAM**
3. Find your service account: `<EMAIL>`
4. Click **Edit** (pencil icon)
5. Click **Add Another Role**
6. Add: **Earth Engine Resource Viewer**
7. Click **Save**

#### Step 2: Enable Earth Engine API
1. Go to **APIs & Services > Library**
2. Search for "Earth Engine API"
3. Click **Enable**

#### Step 3: Register Project for Earth Engine
1. Visit [Google Earth Engine](https://earthengine.google.com/)
2. Sign in with your Google account
3. Register your project: `grand-airport-464420-e3`
4. Wait for approval (usually instant for existing GCP projects)

#### Step 4: Verify Permissions
After making changes, wait 5-10 minutes for permissions to propagate, then:
1. Restart your HuggingFace Space
2. Check the logs for successful GEE initialization
3. Use debug endpoint: `/api/v1/health/debug-gee` (when DEBUG=true)

### Alternative: Create New Service Account
If issues persist, create a new service account:

1. **Create Service Account**:
   - Go to **IAM & Admin > Service Accounts**
   - Click **Create Service Account**
   - Name: `atlas-gee-service-v2`
   - Add roles: **Earth Engine Resource Viewer**

2. **Generate Key**:
   - Click on the new service account
   - Go to **Keys** tab
   - Click **Add Key > Create New Key**
   - Choose **JSON** format
   - Download the key file

3. **Update HuggingFace Secrets**:
   - Extract values using the `extract_env_vars.py` script
   - Update your HuggingFace Space environment variables

### Debug Commands
To troubleshoot, enable debug mode and use these endpoints:
- `/api/v1/health/debug-env` - Environment variables
- `/api/v1/health/debug-gee` - Google Earth Engine status
- `/web/debug/static-files` - Static files status

### Expected Success Indicators
When fixed, you should see:
```
✓ Google Earth Engine initialized with service account: atlas-gee-service-account@...
✓ Project: grand-airport-464420-e3
✅ Google Earth Engine connection test: SUCCESS
```

## 🚀 Deployment Checklist

- [x] Fix Pydantic validation errors
- [x] Fix web UI static file serving
- [ ] Fix Google Earth Engine authentication
- [ ] Test all API endpoints
- [ ] Verify web interface functionality

## 📞 Support
If issues persist, the debug endpoints will provide detailed error information to help troubleshoot further.
