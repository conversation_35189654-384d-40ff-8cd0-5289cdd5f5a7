#!/usr/bin/env python3
"""
Helper script to extract individual environment variables from Google Service Account <PERSON><PERSON><PERSON>
This creates secure environment variables instead of using base64 encoding
"""

import json
from pathlib import Path

def extract_env_variables():
    """Extract environment variables from service account JSON"""
    
    print("🔐 Google Earth Engine - Environment Variable Extractor")
    print("=" * 60)
    
    # Check if service account file exists
    service_account_path = Path("keys/gee-service-account.json")
    
    if not service_account_path.exists():
        print("❌ Service account file not found!")
        print(f"   Expected: {service_account_path}")
        print("   Please ensure your JSON file is in the keys/ directory")
        return
    
    try:
        # Load the service account JSON
        with open(service_account_path, 'r') as f:
            data = json.load(f)
        
        # Extract individual components
        project_id = data.get('project_id')
        client_email = data.get('client_email')
        private_key = data.get('private_key')
        private_key_id = data.get('private_key_id')
        client_id = data.get('client_id')
        
        print("✅ Successfully extracted service account components!")
        print("\n📋 Copy these environment variables to your cloud platform:")
        print("-" * 60)
        
        print("# Google Earth Engine Authentication (Individual Variables)")
        print(f'GOOGLE_CLOUD_PROJECT={project_id}')
        print(f'GOOGLE_SERVICE_ACCOUNT_EMAIL={client_email}')
        print(f'GOOGLE_PRIVATE_KEY_ID={private_key_id}')
        print(f'GOOGLE_CLIENT_ID={client_id}')
        print(f'GOOGLE_PRIVATE_KEY="{private_key}"')
        
        print("\n# Application Settings")
        print("DEBUG=false")
        print("LOG_LEVEL=INFO")
        print("MAX_AOI_AREA_KM2=100.0")
        print("REQUEST_TIMEOUT=300")
        print("OVERPASS_API_URL=https://overpass-api.de/api/interpreter")
        print("OVERPASS_RATE_LIMIT=0.5")
        
        print("\n" + "=" * 60)
        print("🔒 SECURITY EXPLANATION:")
        print("\n✅ Why this is MORE secure than Base64:")
        print("   • Base64 is NOT encryption - anyone can decode it")
        print("   • Individual variables are harder to misuse")
        print("   • Cloud platforms encrypt environment variables")
        print("   • No complete JSON key in a single variable")
        
        print("\n🚀 Platform Setup Instructions:")
        print("\n🤗 Hugging Face Spaces:")
        print("   1. Go to your Space → Settings → Environment Variables")
        print("   2. Add each variable above as a separate entry")
        print("   3. Set Space visibility to Private")
        
        print("\n🚅 Railway:")
        print("   railway variables:set GOOGLE_CLOUD_PROJECT=\"your-project-id\"")
        print("   railway variables:set GOOGLE_SERVICE_ACCOUNT_EMAIL=\"your-email\"")
        print("   # ... continue for each variable")
        
        print("\n🎨 Render:")
        print("   1. Add each variable in your service environment variables")
        print("   2. Mark sensitive variables as 'secret'")
        
        print("\n⚠️  Security Best Practices:")
        print("   • Never commit these values to git")
        print("   • Use private repositories/spaces")
        print("   • Rotate keys every 90 days")
        print("   • Monitor usage in Google Cloud Console")
        
        # Save to a gitignored file for reference
        env_content = f"""# Atlas API - Cloud Environment Variables
# SECURITY: DO NOT COMMIT TO GIT

# Google Earth Engine Authentication
GOOGLE_CLOUD_PROJECT={project_id}
GOOGLE_SERVICE_ACCOUNT_EMAIL={client_email}
GOOGLE_PRIVATE_KEY_ID={private_key_id}
GOOGLE_CLIENT_ID={client_id}
GOOGLE_PRIVATE_KEY="{private_key}"

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
MAX_AOI_AREA_KM2=100.0
REQUEST_TIMEOUT=300
OVERPASS_API_URL=https://overpass-api.de/api/interpreter
OVERPASS_RATE_LIMIT=0.5
"""
        
        with open('.env.cloud', 'w') as f:
            f.write(env_content)
        
        print(f"\n💾 Variables saved to: .env.cloud (git-ignored)")
        
    except json.JSONDecodeError:
        print("❌ Invalid JSON in service account file")
    except KeyError as e:
        print(f"❌ Missing required field in service account: {e}")
    except Exception as e:
        print(f"❌ Error processing service account file: {e}")

if __name__ == "__main__":
    extract_env_variables()