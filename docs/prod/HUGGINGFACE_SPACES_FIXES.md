# HuggingFace Spaces Deployment Fixes

## ✅ Fixed Issues

### 1. Pydantic Validation Error - RESOLVED ✅
**Problem**: Environment variables with trailing newlines causing validation errors
**Solution**: Added field validators to strip whitespace from all environment variables

### 2. Web UI Static Files - RESOLVED ✅  
**Problem**: CSS/JS files not loading due to incorrect `url_for` usage
**Solution**: Added proper `url_for` function to Jinja2 templates and improved static file path detection

## 🔧 CRITICAL: Google Earth Engine Authentication Issues

### Problem 1: Missing Service Usage Consumer Role
**Error**: `roles/serviceusage.serviceUsageConsumer role`

**Root Cause**: Google Cloud requires the Service Usage Consumer role for ANY service account that needs to use Google APIs, including Earth Engine.

### Problem 2: OAuth Scope Error
**Error**: "Invalid OAuth scope or ID token audience provided"

**Root Cause**: Service account lacks proper Earth Engine permissions or project not registered.

### Solution Steps (BOTH REQUIRED)

#### Step 1: Add Service Usage Consumer Role (CRITICAL)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **IAM & Admin > IAM**
3. Find your service account: `<EMAIL>`
4. Click **Edit** (pencil icon)
5. Click **Add Another Role**
6. Add: **Service Usage Consumer** (`roles/serviceusage.serviceUsageConsumer`)
7. Click **Save**

#### Step 2: Add Earth Engine Role to Service Account
1. In the same IAM page
2. Click **Edit** on your service account again
3. Click **Add Another Role**
4. Add: **Earth Engine Resource Viewer** (`roles/earthengine.viewer`)
5. Click **Save**

#### Step 2: Enable Earth Engine API
1. Go to **APIs & Services > Library**
2. Search for "Earth Engine API"
3. Click **Enable**

#### Step 3: Register Project for Earth Engine
1. Visit [Google Earth Engine](https://earthengine.google.com/)
2. Sign in with your Google account
3. Register your project: `grand-airport-464420-e3`
4. Wait for approval (usually instant for existing GCP projects)

#### Step 4: Verify Permissions
After making changes, wait 5-10 minutes for permissions to propagate, then:
1. Restart your HuggingFace Space
2. Check the logs for successful GEE initialization
3. Use debug endpoint: `/api/v1/health/debug-gee` (when DEBUG=true)

### Alternative: Create New Service Account
If issues persist, create a new service account:

1. **Create Service Account**:
   - Go to **IAM & Admin > Service Accounts**
   - Click **Create Service Account**
   - Name: `atlas-gee-service-v2`
   - Add roles: **Earth Engine Resource Viewer**

2. **Generate Key**:
   - Click on the new service account
   - Go to **Keys** tab
   - Click **Add Key > Create New Key**
   - Choose **JSON** format
   - Download the key file

3. **Update HuggingFace Secrets**:
   - Extract values using the `extract_env_vars.py` script
   - Update your HuggingFace Space environment variables

## 🔍 Enhanced Debugging Capabilities

### New Debug Endpoints
The application now includes comprehensive logging to capture raw Google API responses:

#### Available Debug Endpoints:
- `/api/v1/health/debug-gee-detailed` - Comprehensive logging with troubleshooting
- `/api/v1/health/debug-gee-raw` - **Raw HTTP request/response logging**
- `/api/v1/health/debug-gee` - Basic validation and connection test
- `/api/v1/health/debug-env` - Environment variables
- `/web/debug/static-files` - Static files status

#### What the Raw Logs Show:
1. **Service Account Credential Creation**: Step-by-step credential building from environment variables
2. **OAuth Scope Details**: Exact scopes being requested (`earthengine`, `cloud-platform`, etc.)
3. **Raw HTTP Responses**: Complete Google API responses including error details
4. **Authentication Flow**: Multiple authentication methods attempted
5. **Specific Error Guidance**: Targeted solutions based on error patterns

### Key Code Improvements Made:

#### 1. Individual Environment Variable Support ✅
- **Fixed**: Now properly creates service account credentials from individual HuggingFace Secrets
- **Method**: Builds complete JSON credentials file from separate environment variables
- **Validation**: Checks all required fields before attempting authentication

#### 2. Comprehensive Error Handling ✅
- **Multiple Auth Methods**: Tries `google.oauth2.service_account` then falls back to `ee.ServiceAccountCredentials`
- **Explicit OAuth Scopes**: Uses correct Earth Engine scopes
- **Detailed Error Messages**: Specific guidance for each error type

#### 3. Raw API Response Capture ✅
- **HTTP Logging**: Captures all HTTP requests/responses to Google APIs
- **Error Analysis**: Logs complete error responses for debugging
- **Troubleshooting**: Automatic error pattern recognition with solutions

### Testing the Fix

#### Step 1: Check Required Roles
Verify both required roles are assigned to your service account:
- ✅ `roles/serviceusage.serviceUsageConsumer` ⚠️ **CRITICAL**
- ✅ `roles/earthengine.viewer`

#### Step 2: Use Debug Endpoints
Visit these endpoints to get detailed diagnostics:
- **`/api/v1/health/debug-gee-raw`** - See raw Google API responses
- **`/api/v1/health/debug-gee-detailed`** - Get comprehensive diagnostics

#### Step 3: Look for Success Indicators
```
✅ Service account credentials created successfully
✅ Google Earth Engine initialized successfully!
   Service Account: <EMAIL>
   Project: grand-airport-464420-e3
```

### Common Error Patterns & Solutions

#### ❌ Error: `serviceusage.serviceUsageConsumer`
**Solution**: Add `roles/serviceusage.serviceUsageConsumer` role to service account
**Why**: Google Cloud requires this role for ANY service account using Google APIs

#### ❌ Error: "Invalid OAuth scope or ID token audience provided"
**Solutions**:
1. Add `roles/earthengine.viewer` role
2. Enable Earth Engine API in Google Cloud Console
3. Register project at https://earthengine.google.com/
4. Wait up to 24 hours for permissions to propagate

#### ❌ Error: "Permission denied" or "Forbidden"
**Solutions**:
1. Verify service account has both required roles
2. Check that project is registered for Earth Engine
3. Ensure Earth Engine API is enabled

### If Still Not Working

1. **Wait**: Permissions can take up to 24 hours to propagate
2. **Verify Project Registration**: Ensure project is approved for Earth Engine access
3. **Check Environment Variables**: Verify all HuggingFace Secrets are correctly set
4. **Review Raw Logs**: Use `/api/v1/health/debug-gee-raw` to see exact Google API responses
5. **HuggingFace Limitation**: If Google APIs return unexpected errors, this may be a HuggingFace Spaces networking limitation

## 🚀 Deployment Checklist

- [x] Fix Pydantic validation errors
- [x] Fix web UI static file serving
- [x] Add comprehensive Google Earth Engine debugging
- [x] Implement raw API response logging
- [ ] Fix Google Earth Engine authentication (pending role assignment)
- [ ] Test all API endpoints
- [ ] Verify web interface functionality

## 📞 Next Steps

1. **Add the missing role**: `roles/serviceusage.serviceUsageConsumer` to your service account
2. **Test with debug endpoints**: Use `/api/v1/health/debug-gee-raw` to see exact Google responses
3. **Monitor logs**: Look for the success indicators above
4. **Report findings**: If Google APIs still fail, we can determine if it's a HuggingFace Spaces limitation
