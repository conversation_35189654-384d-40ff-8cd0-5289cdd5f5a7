<PERSON><PERSON><PERSON>@MacBook-Pro ~ % curl -X POST "https://kamau1-atlas.hf.space/api/v1/extract-features" \
  -H "Content-Type: application/json" \
  -d '{
    "aoi_boundary": {
      "type": "Polygon",
      "coordinates": [[
        [36.89580910329874, -1.319341139743786],
        [36.897622276592806, -1.318300718323672],
        [36.89597003583963, -1.316016080071957],
        [36.89450018529947, -1.3169599684298745],
        [36.89507954244669, -1.317957486419588],
        [36.89453237180765, -1.318257814338252],
        [36.89531557684, -1.3194698516418033],
        [36.89580910329874, -1.319341139743786]
      ]]
    },
    "data_sources": {
      "microsoft_buildings": {"enabled": true},
      "google_buildings": {"enabled": true},
      "osm_buildings": {"enabled": true},
      "osm_roads": {"enabled": true}
    }
  }'
{"detail":"Feature extraction failed: 2 validation errors for FeatureStats\ntotal_area\n  Field required [type=missing, input_value={'count': 0, 'processing_time': 0.0}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ntotal_length\n  Field required [type=missing, input_value={'count': 0, 'processing_time': 0.0}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing"}%


===== Application Startup at 2025-09-07 12:20:41 =====

🌍 Atlas GIS API - Configuration Status
==================================================
Platform: huggingface_spaces
Environment: healthy
Space ID: kamau1/atlas
Author: kamau1

🔧 Configuration:
   Service: atlas-geospatial-api v1.0.0
   Debug mode: False
   Log level: INFO
   Max AOI area: 100.0 km²
   Request timeout: 300s
   Overpass rate limit: 0.5 req/s

🔑 Google Earth Engine: complete
   Project: grand-airport-464420-e3
   Service Account: <EMAIL>

✅ Configuration Info:
   • Google Earth Engine credentials configured

💡 Recommendations:
   ✅ Running on HuggingFace Spaces - environment variables are injected at runtime
   🔧 Use Space Settings to configure environment variables
   🔒 Use Secrets for sensitive data like Google credentials
   📝 Variables are automatically available as environment variables
==================================================
INFO:src.main:Mounting static files from: src/web/static (exists: True)
INFO:     Started server process [1]
INFO:     Waiting for application startup.
INFO:src.main:Starting Atlas FastAPI Service
INFO:src.main:Service configuration: atlas-geospatial-api v1.0.0
INFO:src.utils.gee_auth:🚀 Starting Google Earth Engine initialization...
INFO:src.utils.gee_auth:🔧 Creating service account credentials from environment variables
INFO:src.utils.gee_auth:   Project ID: grand-airport-464420-e3
INFO:src.utils.gee_auth:   Client Email: <EMAIL>
INFO:src.utils.gee_auth:   Client ID: 116275573697305905318
INFO:src.utils.gee_auth:   Private Key ID: 5ff05269...
INFO:src.utils.gee_auth:   Private Key Length: 1703 chars
INFO:src.utils.gee_auth:✅ Service account credentials file created: /tmp/tmp9mhfoy2w.json
INFO:src.utils.gee_auth:📁 Using credentials file: /tmp/tmp9mhfoy2w.json
INFO:src.utils.gee_auth:🔍 Service Account Key Data:
INFO:src.utils.gee_auth:   Response Type: <class 'dict'>
INFO:src.utils.gee_auth:   Response Content: {'type': 'service_account', 'project_id': 'grand-airport-464420-e3', 'private_key_id': '5ff05269086d4d1425998a76df3d603f81cb45d4', 'private_key': '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCvmdH0UiM/u/Vg\nZZN5xHpt5AGfRF2TnD6tg8EGDtxtM/PGmtVCBy62aU0tmgf3Y4kcF48B3ytbJ1Km\nA2VcM8p6B7cXkIVJj7TWPT0IqKc33W5AEZd5AHxjKxofIXaJVj2M4c617ngO2qPy\nwOk1JTPK64zRM+Dp6uA+IczvI4dGaNWSP4dmny4gNvqkUrMrM+hOW2MgqVyY5eD8\nL4q+fGzAWV8bP7p8uA+bZH38dmr3YYIJ2Y9Ss7lLEgvM1h6Ml6uDJofzkJea/06A\niyjW3wiIGTo/kuBpfVmq0QT7m/tXcJ0KZj+lgG2Sr3+xSSnw642zUCm0yqRyndnq\n1MZha6bdAgMBAAECggEALCJNbgoc2N5tOAqfi7mxiaAGNljQ6hIZgLO+Fj8y8HFf\nwhlVIxC6EVea8aymdut54Okc5LwJ4hGaiKYO2iMcLxp0ToRgs5HeQgBHMD9vM6/A\nD3M8bkmMecIyAQQAjljyBFAWi4xBeX5DOXZ5xoUwpL4Z+NSAoVoKfSMypiH4VF4H\nBI4r0Ik2m1urMtuOTvkFSJOpENKQrz/z1QrHIrk8FSYOZUatsQjW4QyuGVg8csMm\nIfgETsoKGodiGjKLEAl5DspEOFh4fdGSlLQVFXTNPp9+QN6xZXVY/z1VEEbAkNkL\nTnasUg5kZ9Jp97NPMVn2HdUpHcCDywkJ612bnodfcwKBgQDja63ojNpv3RRWNgod\nvYmLUBIoh0XJPW1kXCzI4NUiHobmODwF8FZimpnHOE9LiWtatAajdH/F4ShULRfV\nheGAZDDO+sCaEgmeuQN7kT6fo+T525cwMpnvrRIh46QT7P1/a5fp8KSFjMba9qKl\nWaC7HJOCG8hXEZ4wqQlLUgJm5wKBgQDFqw27r4RlqfrufurS83AnjPZjSdPT3AYu\nkr50iV8KMGT+Yyo0CzgkIlyUvpFq7r2y8HzeaWBJOlDYGwMrzlcYuQ+9XF0fO7sE\nZL4VvKYkQFlSNC1DXxd4/uID2r5YvR2n18x/FV8azaBQdWgUYg78hjoeRRx7Z97w\nLTeBbie/mwKBgBsZHIrGJc0IecDgBYqPqBD6hH9hxvX4pM09knSJHbxICOjrsHNA\ntPpg04wCcKhegnU3WEcnvYXVk1mcWeeuYTVMOuiexk5VceIAx6J0pxKck88V1EkA\n7oi5hZyWJFnfdkE1j89nRiNHp/Y1RyIOsPE6sXTiXJfLWJ8RtTLNXc3nAoGBAJS4\nKdD4MUi8JwuhedPVTKC1Mftp5HIVrKEFzz2mzVbBKnsq6OnXNxoYsEdJSeeGUDt9\nLLW52rO9qG2vhSdES8tSEUGLaJTDP+YCVfHMycOklo9+qj9yIBrsyefUgDoLr3BE\nsJs83R0xcFiYvQH49QWRh9CWduNyNw3FN2muE0HZAoGAfU3KVO/Wh05fKfvJqFV8\nWEs8yWFDIgZLt5vdw0sh+ofRXq1q7drimnTf9slsRgy4sNLcXaNCm8zm7oHomg9p\nlOj/0twZ3L+t3n4lUhWFZhGCigtN0xUjhhr/JNJH7ZkQF3zyvgKiSAS1+H59Ci0i\nW0Wm3nuTEGg0VrFl00QXA48=\n-----END PRIVATE KEY-----', 'client_email': '<EMAIL>', 'client_id': '116275573697305905318', 'auth_uri': 'https://accounts.google.com/o/oauth2/auth', 'token_uri': 'https://oauth2.googleapis.com/token', 'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs', 'universe_domain': 'googleapis.com', 'client_x509_cert_url': 'https://www.googleapis.com/robot/v1/metadata/x509/atlas-gee-service-account%40grand-airport-464420-e3.iam.gserviceaccount.com'}
INFO:src.utils.gee_auth:   type: service_account
INFO:src.utils.gee_auth:   project_id: grand-airport-464420-e3
INFO:src.utils.gee_auth:   private_key_id: 5ff05269086d4d1425998a76df3d603f81cb45d4
INFO:src.utils.gee_auth:   private_key: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************INFO:src.utils.gee_auth:   client_email: <EMAIL>
INFO:src.utils.gee_auth:   client_id: 116275573697305905318
INFO:src.utils.gee_auth:   auth_uri: https://accounts.google.com/o/oauth2/auth
INFO:src.utils.gee_auth:   token_uri: https://oauth2.googleapis.com/token
INFO:src.utils.gee_auth:   auth_provider_x509_cert_url: https://www.googleapis.com/oauth2/v1/certs
INFO:src.utils.gee_auth:   universe_domain: googleapis.com
INFO:src.utils.gee_auth:   client_x509_cert_url: https://www.googleapis.com/robot/v1/metadata/x509/atlas-gee-service-account%40grand-airport-464420-e3.iam.gserviceaccount.com
INFO:src.utils.gee_auth:🔐 Creating service account credentials...
INFO:src.utils.gee_auth:🎯 Using OAuth scopes: ['https://www.googleapis.com/auth/earthengine', 'https://www.googleapis.com/auth/earthengine.readonly', 'https://www.googleapis.com/auth/devstorage.full_control', 'https://www.googleapis.com/auth/cloud-platform']
INFO:src.utils.gee_auth:🔧 Attempting authentication with google.oauth2.service_account...
INFO:src.utils.gee_auth:✅ Service account credentials created successfully
INFO:src.utils.gee_auth:🔍 Google Auth Credentials Object:
INFO:src.utils.gee_auth:   Response Type: <class 'google.oauth2.service_account.Credentials'>
INFO:src.utils.gee_auth:   Response Content: <google.oauth2.service_account.Credentials object at 0x7f73d8106d50>
INFO:src.utils.gee_auth:   Response Attributes: {'token': None, 'expiry': None, '_quota_project_id': None, '_trust_boundary': {'locations': [], 'encoded_locations': '0x0'}, '_universe_domain': 'googleapis.com', '_use_non_blocking_refresh': False, '_refresh_worker': <google.auth._refresh_worker.RefreshThreadManager object at 0x7f73d3ff4390>, '_scopes': ['https://www.googleapis.com/auth/earthengine', 'https://www.googleapis.com/auth/earthengine.readonly', 'https://www.googleapis.com/auth/devstorage.full_control', 'https://www.googleapis.com/auth/cloud-platform'], '_default_scopes': None, '_cred_file_path': None, '_signer': <google.auth.crypt._cryptography_rsa.RSASigner object at 0x7f73cfe4a3d0>, '_service_account_email': '<EMAIL>', '_subject': None, '_project_id': 'grand-airport-464420-e3', '_token_uri': 'https://oauth2.googleapis.com/token', '_always_use_jwt_access': False, '_jwt_credentials': None, '_additional_claims': {}}
INFO:src.utils.gee_auth:🌍 Initializing Earth Engine with project: grand-airport-464420-e3
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:google_auth_httplib2:Making request: POST https://oauth2.googleapis.com/token
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): oauth2.googleapis.com:443
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): earthengine.googleapis.com:443
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.utils.gee_auth:✅ Google Earth Engine initialized successfully!
INFO:src.utils.gee_auth:   Service Account: <EMAIL>
INFO:src.utils.gee_auth:   Project: grand-airport-464420-e3
INFO:src.main:Google Earth Engine initialized successfully
✓ Using individual environment variables for authentication
INFO:src.utils.gee_auth:🧪 Testing Earth Engine connection...
INFO:src.utils.gee_auth:✅ Test image created successfully
INFO:src.utils.gee_auth:🔍 Fetching image metadata (this will test API access)...
DEBUG:googleapiclient.discovery:URL being requested: POST https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.utils.gee_auth:✅ Google Earth Engine connection test: SUCCESS
INFO:src.utils.gee_auth:🔍 Test Image Info:
INFO:src.utils.gee_auth:   Response Type: <class 'dict'>
INFO:src.utils.gee_auth:   Response Content: {'type': 'Image', 'bands': [{'id': 'elevation', 'data_type': {'type': 'PixelType', 'precision': 'int', 'min': -32768, 'max': 32767}, 'dimensions': [1296001, 417601], 'crs': 'EPSG:4326', 'crs_transform': [0.0002777777777777778, 0, -180.0001388888889, 0, -0.0002777777777777778, 60.00013888888889]}], 'version': 1641990767055141, 'id': 'USGS/SRTMGL1_003', 'properties': {'system:visualization_0_min': '0.0', 'type_name': 'Image', 'keywords': ['dem', 'elevation', 'geophysical', 'nasa', 'srtm', 'topography', 'usgs'], 'thumb': 'https://mw1.google.com/ges/dd/images/SRTM90_V4_thumb.png', 'description': '<p>The Shuttle Radar Topography Mission (SRTM, see <a href="https://onlinelibrary.wiley.com/doi/10.1029/2005RG000183/full">Farr\net al. 2007</a>)\ndigital elevation data is an international research effort that\nobtained digital elevation models on a near-global scale. This\nSRTM V3 product (SRTM Plus) is provided by NASA JPL\nat a resolution of 1 arc-second (approximately 30m).</p><p>This dataset has undergone a void-filling process using open-source data\n(ASTER GDEM2, GMTED2010, and NED), as opposed to other versions that\ncontain voids or have been void-filled with commercial sources.\nFor more information on the different versions see the\n<a href="https://lpdaac.usgs.gov/documents/13/SRTM_Quick_Guide.pdf">SRTM Quick Guide</a>.</p><p>Documentation:</p><ul><li><p><a href="https://lpdaac.usgs.gov/documents/179/SRTM_User_Guide_V3.pdf">User&#39;s Guide</a></p></li><li><p><a href="https://lpdaac.usgs.gov/documents/13/SRTM_Quick_Guide.pdf">General Documentation</a></p></li><li><p><a href="https://doi.org/10.1029/2005RG000183">Algorithm Theoretical Basis Document (ATBD)</a></p></li></ul><p><b>Provider: <a href="https://cmr.earthdata.nasa.gov/search/concepts/C1000000240-LPDAAC_ECS.html">NASA / USGS / JPL-Caltech</a></b><br><p><b>Bands</b><table class="eecat"><tr><th scope="col">Name</th><th scope="col">Description</th></tr><tr><td>elevation</td><td><p>Elevation</p></td></tr></table><p><b>Terms of Use</b><br><p>Unless otherwise noted, images and video on JPL public\nweb sites (public sites ending with a jpl.nasa.gov address) may\nbe used for any purpose without prior permission. For more information\nand exceptions visit the <a href="https://www.jpl.nasa.gov/imagepolicy/">JPL Image Use Policy site</a>.</p><p><b>Suggested citation(s)</b><ul><li><p>Farr, T.G., Rosen, P.A., Caro, E., Crippen, R., Duren, R., Hensley,\nS., Kobrick, M., Paller, M., Rodriguez, E., Roth, L., Seal, D.,\nShaffer, S., Shimada, J., Umland, J., Werner, M., Oskin, M., Burbank,\nD., and Alsdorf, D.E., 2007, The shuttle radar topography mission:\nReviews of Geophysics, v. 45, no. 2, RG2004, at\n<a href="https://doi.org/10.1029/2005RG000183">https://doi.org/10.1029/2005RG000183</a>.</p></li></ul><style>\n  table.eecat {\n  border: 1px solid black;\n  border-collapse: collapse;\n  font-size: 13px;\n  }\n  table.eecat td, tr, th {\n  text-align: left; vertical-align: top;\n  border: 1px solid gray; padding: 3px;\n  }\n  td.nobreak { white-space: nowrap; }\n</style>', 'source_tags': ['nasa', 'usgs'], 'visualization_0_max': '6000.0', 'title': 'NASA SRTM Digital Elevation 30m', 'product_tags': ['srtm', 'elevation', 'topography', 'dem', 'geophysical'], 'provider': 'NASA / USGS / JPL-Caltech', 'visualization_0_min': '0.0', 'visualization_0_name': 'Elevation', 'date_range': [************, ************], 'system:visualization_0_gamma': '1.6', 'period': 0, 'system:visualization_0_bands': 'elevation', 'provider_url': 'https://cmr.earthdata.nasa.gov/search/concepts/C1000000240-LPDAAC_ECS.html', 'visualization_0_gamma': '1.6', 'sample': 'https://mw1.google.com/ges/dd/images/SRTM90_V4_sample.png', 'tags': ['dem', 'elevation', 'geophysical', 'nasa', 'srtm', 'topography', 'usgs'], 'system:visualization_0_max': '6000.0', 'system:visualization_0_name': 'Elevation', 'system:asset_size': 132792638252, 'visualization_0_bands': 'elevation'}}
INFO:src.utils.gee_auth:   type: Image
INFO:src.utils.gee_auth:   bands: [{'id': 'elevation', 'data_type': {'type': 'PixelType', 'precision': 'int', 'min': -32768, 'max': 32767}, 'dimensions': [1296001, 417601], 'crs': 'EPSG:4326', 'crs_transform': [0.0002777777777777778, 0, -180.0001388888889, 0, -0.0002777777777777778, 60.00013888888889]}]
INFO:src.utils.gee_auth:   version: 1641990767055141
INFO:src.utils.gee_auth:   id: USGS/SRTMGL1_003
INFO:src.utils.gee_auth:   properties: {'system:visualization_0_min': '0.0', 'type_name': 'Image', 'keywords': ['dem', 'elevation', 'geophysical', 'nasa', 'srtm', 'topography', 'usgs'], 'thumb': 'https://mw1.google.com/ges/dd/images/SRTM90_V4_thumb.png', 'description': '<p>The Shuttle Radar Topography Mission (SRTM, see <a href="https://onlinelibrary.wiley.com/doi/10.1029/2005RG000183/full">Farr\net al. 2007</a>)\ndigital elevation data is an international research effort that\nobtained digital elevation models on a near-global scale. This\nSRTM V3 product (SRTM Plus) is provided by NASA JPL\nat a resolution of 1 arc-second (approximately 30m).</p><p>This dataset has undergone a void-filling process using open-source data\n(ASTER GDEM2, GMTED2010, and NED), as opposed to other versions that\ncontain voids or have been void-filled with commercial sources.\nFor more information on the different versions see the\n<a href="https://lpdaac.usgs.gov/documents/13/SRTM_Quick_Guide.pdf">SRTM Quick Guide</a>.</p><p>Documentation:</p><ul><li><p><a href="https://lpdaac.usgs.gov/documents/179/SRTM_User_Guide_V3.pdf">User&#39;s Guide</a></p></li><li><p><a href="https://lpdaac.usgs.gov/documents/13/SRTM_Quick_Guide.pdf">General Documentation</a></p></li><li><p><a href="https://doi.org/10.1029/2005RG000183">Algorithm Theoretical Basis Document (ATBD)</a></p></li></ul><p><b>Provider: <a href="https://cmr.earthdata.nasa.gov/search/concepts/C1000000240-LPDAAC_ECS.html">NASA / USGS / JPL-Caltech</a></b><br><p><b>Bands</b><table class="eecat"><tr><th scope="col">Name</th><th scope="col">Description</th></tr><tr><td>elevation</td><td><p>Elevation</p></td></tr></table><p><b>Terms of Use</b><br><p>Unless otherwise noted, images and video on JPL public\nweb sites (public sites ending with a jpl.nasa.gov address) may\nbe used for any purpose without prior permission. For more information\nand exceptions visit the <a href="https://www.jpl.nasa.gov/imagepolicy/">JPL Image Use Policy site</a>.</p><p><b>Suggested citation(s)</b><ul><li><p>Farr, T.G., Rosen, P.A., Caro, E., Crippen, R., Duren, R., Hensley,\nS., Kobrick, M., Paller, M., Rodriguez, E., Roth, L., Seal, D.,\nShaffer, S., Shimada, J., Umland, J., Werner, M., Oskin, M., Burbank,\nD., and Alsdorf, D.E., 2007, The shuttle radar topography mission:\nReviews of Geophysics, v. 45, no. 2, RG2004, at\n<a href="https://doi.org/10.1029/2005RG000183">https://doi.org/10.1029/2005RG000183</a>.</p></li></ul><style>\n  table.eecat {\n  border: 1px solid black;\n  border-collapse: collapse;\n  font-size: 13px;\n  }\n  table.eecat td, tr, th {\n  text-align: left; vertical-align: top;\n  border: 1px solid gray; padding: 3px;\n  }\n  td.nobreak { white-space: nowrap; }\n</style>', 'source_tags': ['nasa', 'usgs'], 'visualization_0_max': '6000.0', 'title': 'NASA SRTM Digital Elevation 30m', 'product_tags': ['srtm', 'elevation', 'topography', 'dem', 'geophysical'], 'provider': 'NASA / USGS / JPL-Caltech', 'visualization_0_min': '0.0', 'visualization_0_name': 'Elevation', 'date_range': [************, ************], 'system:visualization_0_gamma': '1.6', 'period': 0, 'system:visualization_0_bands': 'elevation', 'provider_url': 'https://cmr.earthdata.nasa.gov/search/concepts/C1000000240-LPDAAC_ECS.html', 'visualization_0_gamma': '1.6', 'sample': 'https://mw1.google.com/ges/dd/images/SRTM90_V4_sample.png', 'tags': ['dem', 'elevation', 'geophysical', 'nasa', 'srtm', 'topography', 'usgs'], 'system:visualization_0_max': '6000.0', 'system:visualization_0_name': 'Elevation', 'system:asset_size': 132792638252, 'visualization_0_bands': 'elevation'}
INFO:src.main:Google Earth Engine connection test passed
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:7860 (Press CTRL+C to quit)
INFO:src.api.routes.extract:Starting feature extraction request
INFO:src.core.processors.feature_aggregator:Starting processing job 5e835b4c-3269-48c3-b8b4-a54c2708799a
INFO:src.core.processors.feature_aggregator:AOI validated: 0.08 km²
INFO:src.core.processors.feature_aggregator:Processing 4 data sources: ['microsoft_buildings', 'google_buildings', 'osm_buildings', 'osm_roads']
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:google_auth_httplib2:Making request: POST https://oauth2.googleapis.com/token
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
INFO:src.core.data_sources.base.microsoft_buildings:Google Earth Engine initialized successfully
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:google_auth_httplib2:Making request: POST https://oauth2.googleapis.com/token
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
INFO:src.core.data_sources.base.google_buildings:Google Earth Engine initialized successfully
INFO:src.core.data_sources.base.microsoft_buildings:Starting feature extraction from microsoft_buildings
INFO:src.core.data_sources.base.google_buildings:Starting feature extraction from google_buildings
INFO:src.core.data_sources.base.osm_buildings:Starting feature extraction from osm_buildings
INFO:src.core.data_sources.base.osm_roads:Starting feature extraction from osm_roads
DEBUG:googleapiclient.discovery:URL being requested: POST https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: POST https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
/home/<USER>/app/src/core/data_sources/openstreetmap.py:111: UserWarning: The `geometries` module and `geometries_from_X` functions have been renamed the `features` module and `features_from_X` functions. Use these instead. The `geometries` module and function names are deprecated and will be removed in a future release.
  buildings_gdf = ox.geometries_from_polygon(
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): overpass-api.de:443
DEBUG:urllib3.connectionpool:https://overpass-api.de:443 "GET /api/status HTTP/1.1" 200 180
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): overpass-api.de:443
DEBUG:urllib3.connectionpool:https://overpass-api.de:443 "POST /api/interpreter HTTP/1.1" 200 13055
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): overpass-api.de:443
DEBUG:urllib3.connectionpool:https://overpass-api.de:443 "GET /api/status HTTP/1.1" 200 178
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): overpass-api.de:443
DEBUG:urllib3.connectionpool:https://overpass-api.de:443 "POST /api/interpreter HTTP/1.1" 200 22382
INFO:src.core.data_sources.base.microsoft_buildings:Extracted 54 features from microsoft_buildings in 13.79 seconds
ERROR:src.core.processors.feature_aggregator:Failed to process microsoft_buildings: 'Settings' object has no attribute 'max_features_per_source'
INFO:src.core.data_sources.base.google_buildings:Extracted 136 features from google_buildings in 13.81 seconds
ERROR:src.core.processors.feature_aggregator:Failed to process google_buildings: 'Settings' object has no attribute 'max_features_per_source'
INFO:src.core.data_sources.base.osm_buildings:Extracted 163 features from osm_buildings in 13.85 seconds
ERROR:src.core.processors.feature_aggregator:Failed to process osm_buildings: 'Settings' object has no attribute 'max_features_per_source'
INFO:src.core.data_sources.base.osm_roads:Extracted 14 features from osm_roads in 13.88 seconds
ERROR:src.core.processors.feature_aggregator:Failed to process osm_roads: 'Settings' object has no attribute 'max_features_per_source'
ERROR:src.core.processors.feature_aggregator:Source microsoft_buildings failed: 2 validation errors for FeatureStats
total_area
  Field required [type=missing, input_value={'count': 0, 'processing_time': 13.81272268295288}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
total_length
  Field required [type=missing, input_value={'count': 0, 'processing_time': 13.81272268295288}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
ERROR:src.core.processors.feature_aggregator:Job 5e835b4c-3269-48c3-b8b4-a54c2708799a failed after 14.64s: 2 validation errors for FeatureStats
total_area
  Field required [type=missing, input_value={'count': 0, 'processing_time': 0.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
total_length
  Field required [type=missing, input_value={'count': 0, 'processing_time': 0.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
ERROR:src.api.routes.extract:Processing error: Feature extraction failed: 2 validation errors for FeatureStats
total_area
  Field required [type=missing, input_value={'count': 0, 'processing_time': 0.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
total_length
  Field required [type=missing, input_value={'count': 0, 'processing_time': 0.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
INFO:     10.16.47.58:7753 - "POST /api/v1/extract-features HTTP/1.1" 500 Internal Server Error
 