===== Build Queued at 2025-09-07 09:10:31 / Commit SHA: 5258e76 =====

--> FROM docker.io/library/python:3.11-slim@sha256:1d6131b5d479888b43200645e03a78443c7157efbdb730e6b48129740727c312
DONE 0.9s

DONE 1.5s

DONE 1.5s

--> RUN apt-get update && apt-get install -y     gdal-bin     libgdal-dev     libproj-dev     libgeos-dev     libspatialindex-dev     build-essential     curl     wget     git     && rm -rf /var/lib/apt/lists/*
Get:1 http://deb.debian.org/debian trixie InRelease [140 kB]
Get:2 http://deb.debian.org/debian trixie-updates InRelease [47.1 kB]
Get:3 http://deb.debian.org/debian-security trixie-security InRelease [43.4 kB]
Get:4 http://deb.debian.org/debian trixie/main amd64 Packages [9669 kB]
Get:5 http://deb.debian.org/debian trixie-updates/main amd64 Packages [2432 B]
Get:6 http://deb.debian.org/debian-security trixie-security/main amd64 Packages [33.1 kB]
Fetched 9935 kB in 1s (13.5 MB/s)
Reading package lists...
Reading package lists...
Building dependency tree...
Reading state information...
The following additional packages will be installed:
  autoconf automake autotools-dev bash-completion binutils binutils-common
  binutils-x86-64-linux-gnu bzip2 cfortran comerr-dev cpp cpp-14
  cpp-14-x86-64-linux-gnu cpp-x86-64-linux-gnu default-libmysqlclient-dev
  dirmngr dpkg-dev fakeroot file fontconfig-config fonts-dejavu-core
  fonts-dejavu-mono g++ g++-14 g++-14-x86-64-linux-gnu g++-x86-64-linux-gnu
  gcc gcc-14 gcc-14-x86-64-linux-gnu gcc-x86-64-linux-gnu gdal-data
  gdal-plugins gfortran gfortran-14 gfortran-14-x86-64-linux-gnu
  gfortran-x86-64-linux-gnu git-man gnupg gnupg-l10n gnupg-utils gpg gpg-agent
  gpg-wks-client gpgconf gpgsm gpgv hdf5-helpers icu-devtools krb5-locales
  krb5-multidev less libabsl20240722 libaec-dev libaec0 libalgorithm-diff-perl
  libalgorithm-diff-xs-perl libalgorithm-merge-perl libaom-dev libaom3
  libarmadillo-dev libarmadillo14 libarpack2-dev libarpack2t64 libasan8
  libassuan9 libatomic1 libavif-dev libavif16 libbinutils libblas-dev libblas3
  libblosc-dev libblosc1 libboost-dev libboost1.83-dev libbrotli-dev
  libbrotli1 libc-dev-bin libc6-dev libcbor0.10 libcc1-0 libcfitsio-dev
  libcfitsio-doc libcfitsio10t64 libcom-err2 libcrypt-dev libctf-nobfd0
  libctf0 libcurl3t64-gnutls libcurl4-openssl-dev libcurl4t64 libdav1d-dev
  libdav1d7 libde265-0 libdeflate-dev libdeflate0 libdpkg-perl libedit2
  liberror-perl libevent-2.1-7t64 libexpat1 libexpat1-dev libfakeroot
  libfido2-1 libfile-fcntllock-perl libfontconfig1 libfreetype6 libfreexl-dev
  libfreexl1 libfyba-dev libfyba0t64 libgav1-1 libgcc-14-dev libgcrypt20
  libgdal36 libgdbm-compat4t64 libgeos-c1t64 libgeos3.13.1 libgeotiff-dev
  libgeotiff5 libgfortran-14-dev libgfortran5 libgif-dev libgif7 libgmp-dev
  libgmpxx4ldbl libgnutls-dane0t64 libgnutls-openssl27t64 libgnutls28-dev
  libgnutls30t64 libgomp1 libgpg-error-l10n libgpg-error0 libgpgme11t64
  libgpgmepp6t64 libgprofng0 libgssapi-krb5-2 libgssrpc4t64 libhdf4-0-alt
  libhdf4-alt-dev libhdf5-310 libhdf5-cpp-310 libhdf5-dev libhdf5-fortran-310
  libhdf5-hl-310 libhdf5-hl-cpp-310 libhdf5-hl-fortran-310 libheif-dev
  libheif-plugin-aomenc libheif-plugin-dav1d libheif-plugin-libde265
  libheif-plugin-x265 libheif1 libhwasan0 libicu-dev libicu76 libidn2-0
  libidn2-dev libisl23 libitm1 libjansson4 libjbig-dev libjbig0 libjpeg-dev
  libjpeg62-turbo libjpeg62-turbo-dev libjson-c-dev libjson-c5 libk5crypto3
  libkadm5clnt-mit12 libkadm5srv-mit12 libkdb5-10t64 libkeyutils1 libkml-dev
  libkmlbase1t64 libkmlconvenience1t64 libkmldom1t64 libkmlengine1t64
  libkmlregionator1t64 libkmlxsd1t64 libkrb5-3 libkrb5-dev libkrb5support0
  libksba8 liblapack-dev liblapack3 liblcms2-2 libldap-common libldap-dev
  libldap2 liblerc-dev liblerc4 liblocale-gettext-perl liblsan0 libltdl-dev
  libltdl7 liblz4-dev liblzma-dev libmagic-mgc libmagic1t64 libmariadb-dev
  libmariadb-dev-compat libmariadb3 libminizip-dev libminizip1t64 libmpc3
  libmpfr6 libnetcdf-dev libnetcdf22 libnghttp2-14 libnghttp2-dev libnghttp3-9
  libnghttp3-dev libngtcp2-16 libngtcp2-crypto-gnutls8 libnpth0t64 libnspr4
  libnss3 libnuma1 libodbc2 libodbccr2 libodbcinst2 libogdi-dev libogdi4.1
  libopenjp2-7 libopenjp2-7-dev libp11-kit-dev libp11-kit0 libpcre2-16-0
  libpcre2-32-0 libpcre2-8-0 libpcre2-dev libpcre2-posix3 libperl5.40
  libpkgconf3 libpng-dev libpng-tools libpng16-16t64 libpoppler-dev
  libpoppler-private-dev libpoppler147 libpq-dev libpq5 libproj25 libpsl-dev
  libpsl5t64 libpython3-stdlib libpython3.13-minimal libpython3.13-stdlib
  libqhull-dev libqhull-r8.0 libqhull8.0 libqhullcpp8.0 libquadmath0
  librav1e-dev librav1e0.7 librtmp-dev librtmp1 librttopo-dev librttopo1
  libsasl2-2 libsasl2-modules libsasl2-modules-db libsframe1 libsharpyuv-dev
  libsharpyuv0 libsnappy1v5 libspatialindex-c8 libspatialindex8
  libspatialite-dev libspatialite8t64 libsqlite3-dev libssh2-1-dev
  libssh2-1t64 libssl-dev libstdc++-14-dev libsuperlu-dev libsuperlu7
  libsvtav1enc-dev libsvtav1enc2 libsz2 libtasn1-6 libtasn1-6-dev libtasn1-doc
  libtiff-dev libtiff6 libtiffxx6 libtirpc-common libtirpc-dev libtirpc3t64
  libtool libtsan2 libubsan1 libunbound8 libunistring5 liburiparser-dev
  liburiparser1 libwebp-dev libwebp7 libwebpdecoder3 libwebpdemux2 libwebpmux3
  libx11-6 libx11-data libx265-215 libxau6 libxcb1 libxdmcp6 libxerces-c-dev
  libxerces-c3.2t64 libxext6 libxml2 libxml2-dev libxmuu1 libxxhash-dev
  libyuv-dev libyuv0 libzstd-dev linux-libc-dev m4 make manpages manpages-dev
  mariadb-common media-types mysql-common nettle-dev openssh-client patch perl
  perl-modules-5.40 pinentry-curses pkgconf pkgconf-bin poppler-data proj-bin
  proj-data publicsuffix python3 python3-gdal python3-minimal python3-numpy
  python3-numpy-dev python3.13 python3.13-minimal rpcsvc-proto unixodbc-common
  unixodbc-dev xauth xz-utils zlib1g-dev
Suggested packages:
  autoconf-archive gnu-standards autoconf-doc gettext binutils-doc gprofng-gui
  binutils-gold bzip2-doc doc-base cpp-doc gcc-14-locales cpp-14-doc
  dbus-user-session libpam-systemd pinentry-gnome3 tor debian-keyring
  debian-tag2upload-keyring g++-multilib g++-14-multilib gcc-14-doc
  gcc-multilib flex bison gdb gcc-doc gcc-14-multilib gdb-x86-64-linux-gnu
  libgdal-grass gfortran-multilib gfortran-doc gfortran-14-multilib
  gfortran-14-doc libcoarrays-dev gettext-base git-doc git-email git-gui gitk
  gitweb git-cvs git-mediawiki git-svn gpg-wks-server parcimonie xloadimage
  scdaemon tpm2daemon krb5-doc libitpp-dev liblapack-doc libboost-doc
  libboost1.83-doc libboost-atomic1.83-dev libboost-chrono1.83-dev
  libboost-container1.83-dev libboost-context1.83-dev
  libboost-contract1.83-dev libboost-coroutine1.83-dev
  libboost-date-time1.83-dev libboost-exception1.83-dev libboost-fiber1.83-dev
  libboost-filesystem1.83-dev libboost-graph-parallel1.83-dev
  libboost-graph1.83-dev libboost-iostreams1.83-dev libboost-json1.83-dev
  libboost-locale1.83-dev libboost-log1.83-dev libboost-math1.83-dev
  libboost-mpi-python1.83-dev libboost-mpi1.83-dev libboost-nowide1.83-dev
  libboost-numpy1.83-dev libboost-program-options1.83-dev
  libboost-python1.83-dev libboost-random1.83-dev libboost-regex1.83-dev
  libboost-serialization1.83-dev libboost-stacktrace1.83-dev
  libboost-system1.83-dev libboost-test1.83-dev libboost-thread1.83-dev
  libboost-timer1.83-dev libboost-type-erasure1.83-dev libboost-url1.83-dev
  libboost-wave1.83-dev libboost1.83-tools-dev libmpfrc++-dev libntl-dev
  libc-devtools glibc-doc libcurl4-doc libidn-dev sensible-utils bzr rng-tools
  libgdal-doc libgeotiff-epsg geotiff-bin gmp-doc libgmp10-doc libmpfr-dev
  dns-root-data gnutls-bin gnutls-doc krb5-user hdf4-tools libhdf5-doc
  libheif-plugin-ffmpegdec libheif-plugin-jpegdec libheif-plugin-jpegenc
  libheif-plugin-j2kdec libheif-plugin-j2kenc libheif-plugin-kvazaar
  libheif-plugin-rav1e libheif-plugin-svtenc icu-doc liblcms2-utils
  libtool-doc liblzma-doc netcdf-bin netcdf-doc libnghttp2-doc odbc-postgresql
  tdsodbc ogdi-bin p11-kit-doc libfreetype-dev postgresql-doc-17
  libsasl2-modules-gssapi-mit | libsasl2-modules-gssapi-heimdal
  libsasl2-modules-ldap libsasl2-modules-otp libsasl2-modules-sql sqlite3-doc
  libssl-doc libstdc++-14-doc libsuperlu-doc gcj-jdk libxerces-c-doc m4-doc
  make-doc man-browser keychain libpam-ssh monkeysphere ssh-askpass ed
  diffutils-doc perl-doc libterm-readline-gnu-perl
  | libterm-readline-perl-perl libtap-harness-archive-perl pinentry-doc
  poppler-utils ghostscript fonts-japanese-mincho | fonts-ipafont-mincho
  fonts-japanese-gothic | fonts-ipafont-gothic fonts-arphic-ukai
  fonts-arphic-uming fonts-nanum python3-doc python3-tk python3-venv
  python-numpy-doc python3-dev python3-pytest python3.13-venv python3.13-doc
  binfmt-support
The following NEW packages will be installed:
  autoconf automake autotools-dev bash-completion binutils binutils-common
  binutils-x86-64-linux-gnu build-essential bzip2 cfortran comerr-dev cpp
  cpp-14 cpp-14-x86-64-linux-gnu cpp-x86-64-linux-gnu curl
  default-libmysqlclient-dev dirmngr dpkg-dev fakeroot file fontconfig-config
  fonts-dejavu-core fonts-dejavu-mono g++ g++-14 g++-14-x86-64-linux-gnu
  g++-x86-64-linux-gnu gcc gcc-14 gcc-14-x86-64-linux-gnu gcc-x86-64-linux-gnu
  gdal-bin gdal-data gdal-plugins gfortran gfortran-14
  gfortran-14-x86-64-linux-gnu gfortran-x86-64-linux-gnu git git-man gnupg
  gnupg-l10n gnupg-utils gpg gpg-agent gpg-wks-client gpgconf gpgsm gpgv
  hdf5-helpers icu-devtools krb5-locales krb5-multidev less libabsl20240722
  libaec-dev libaec0 libalgorithm-diff-perl libalgorithm-diff-xs-perl
  libalgorithm-merge-perl libaom-dev libaom3 libarmadillo-dev libarmadillo14
  libarpack2-dev libarpack2t64 libasan8 libassuan9 libatomic1 libavif-dev
  libavif16 libbinutils libblas-dev libblas3 libblosc-dev libblosc1
  libboost-dev libboost1.83-dev libbrotli-dev libbrotli1 libc-dev-bin
  libc6-dev libcbor0.10 libcc1-0 libcfitsio-dev libcfitsio-doc libcfitsio10t64
  libcom-err2 libcrypt-dev libctf-nobfd0 libctf0 libcurl3t64-gnutls
  libcurl4-openssl-dev libcurl4t64 libdav1d-dev libdav1d7 libde265-0
  libdeflate-dev libdeflate0 libdpkg-perl libedit2 liberror-perl
  libevent-2.1-7t64 libexpat1 libexpat1-dev libfakeroot libfido2-1
  libfile-fcntllock-perl libfontconfig1 libfreetype6 libfreexl-dev libfreexl1
  libfyba-dev libfyba0t64 libgav1-1 libgcc-14-dev libgcrypt20 libgdal-dev
  libgdal36 libgdbm-compat4t64 libgeos-c1t64 libgeos-dev libgeos3.13.1
  libgeotiff-dev libgeotiff5 libgfortran-14-dev libgfortran5 libgif-dev
  libgif7 libgmp-dev libgmpxx4ldbl libgnutls-dane0t64 libgnutls-openssl27t64
  libgnutls28-dev libgnutls30t64 libgomp1 libgpg-error-l10n libgpg-error0
  libgpgme11t64 libgpgmepp6t64 libgprofng0 libgssapi-krb5-2 libgssrpc4t64
  libhdf4-0-alt libhdf4-alt-dev libhdf5-310 libhdf5-cpp-310 libhdf5-dev
  libhdf5-fortran-310 libhdf5-hl-310 libhdf5-hl-cpp-310 libhdf5-hl-fortran-310
  libheif-dev libheif-plugin-aomenc libheif-plugin-dav1d
  libheif-plugin-libde265 libheif-plugin-x265 libheif1 libhwasan0 libicu-dev
  libicu76 libidn2-0 libidn2-dev libisl23 libitm1 libjansson4 libjbig-dev
  libjbig0 libjpeg-dev libjpeg62-turbo libjpeg62-turbo-dev libjson-c-dev
  libjson-c5 libk5crypto3 libkadm5clnt-mit12 libkadm5srv-mit12 libkdb5-10t64
  libkeyutils1 libkml-dev libkmlbase1t64 libkmlconvenience1t64 libkmldom1t64
  libkmlengine1t64 libkmlregionator1t64 libkmlxsd1t64 libkrb5-3 libkrb5-dev
  libkrb5support0 libksba8 liblapack-dev liblapack3 liblcms2-2 libldap-common
  libldap-dev libldap2 liblerc-dev liblerc4 liblocale-gettext-perl liblsan0
  libltdl-dev libltdl7 liblz4-dev liblzma-dev libmagic-mgc libmagic1t64
  libmariadb-dev libmariadb-dev-compat libmariadb3 libminizip-dev
  libminizip1t64 libmpc3 libmpfr6 libnetcdf-dev libnetcdf22 libnghttp2-14
  libnghttp2-dev libnghttp3-9 libnghttp3-dev libngtcp2-16
  libngtcp2-crypto-gnutls8 libnpth0t64 libnspr4 libnss3 libnuma1 libodbc2
  libodbccr2 libodbcinst2 libogdi-dev libogdi4.1 libopenjp2-7 libopenjp2-7-dev
  libp11-kit-dev libp11-kit0 libpcre2-16-0 libpcre2-32-0 libpcre2-dev
  libpcre2-posix3 libperl5.40 libpkgconf3 libpng-dev libpng-tools
  libpng16-16t64 libpoppler-dev libpoppler-private-dev libpoppler147 libpq-dev
  libpq5 libproj-dev libproj25 libpsl-dev libpsl5t64 libpython3-stdlib
  libpython3.13-minimal libpython3.13-stdlib libqhull-dev libqhull-r8.0
  libqhull8.0 libqhullcpp8.0 libquadmath0 librav1e-dev librav1e0.7 librtmp-dev
  librtmp1 librttopo-dev librttopo1 libsasl2-2 libsasl2-modules
  libsasl2-modules-db libsframe1 libsharpyuv-dev libsharpyuv0 libsnappy1v5
  libspatialindex-c8 libspatialindex-dev libspatialindex8 libspatialite-dev
  libspatialite8t64 libsqlite3-dev libssh2-1-dev libssh2-1t64 libssl-dev
  libstdc++-14-dev libsuperlu-dev libsuperlu7 libsvtav1enc-dev libsvtav1enc2
  libsz2 libtasn1-6 libtasn1-6-dev libtasn1-doc libtiff-dev libtiff6
  libtiffxx6 libtirpc-common libtirpc-dev libtirpc3t64 libtool libtsan2
  libubsan1 libunbound8 libunistring5 liburiparser-dev liburiparser1
  libwebp-dev libwebp7 libwebpdecoder3 libwebpdemux2 libwebpmux3 libx11-6
  libx11-data libx265-215 libxau6 libxcb1 libxdmcp6 libxerces-c-dev
  libxerces-c3.2t64 libxext6 libxml2 libxml2-dev libxmuu1 libxxhash-dev
  libyuv-dev libyuv0 libzstd-dev linux-libc-dev m4 make manpages manpages-dev
  mariadb-common media-types mysql-common nettle-dev openssh-client patch perl
  perl-modules-5.40 pinentry-curses pkgconf pkgconf-bin poppler-data proj-bin
  proj-data publicsuffix python3 python3-gdal python3-minimal python3-numpy
  python3-numpy-dev python3.13 python3.13-minimal rpcsvc-proto unixodbc-common
  unixodbc-dev wget xauth xz-utils zlib1g-dev
The following packages will be upgraded:
  libpcre2-8-0
1 upgraded, 359 newly installed, 0 to remove and 5 not upgraded.
Need to get 266 MB of archives.
After this operation, 1208 MB of additional disk space will be used.
Get:1 http://deb.debian.org/debian trixie/main amd64 libexpat1 amd64 2.7.1-2 [108 kB]
Get:2 http://deb.debian.org/debian trixie/main amd64 liblocale-gettext-perl amd64 1.07-7+b1 [15.3 kB]
Get:3 http://deb.debian.org/debian trixie/main amd64 poppler-data all 0.4.12-1 [1601 kB]
Get:4 http://deb.debian.org/debian trixie/main amd64 libpython3.13-minimal amd64 3.13.5-2 [862 kB]
Get:5 http://deb.debian.org/debian trixie/main amd64 python3.13-minimal amd64 3.13.5-2 [2224 kB]
Get:6 http://deb.debian.org/debian trixie/main amd64 python3-minimal amd64 3.13.5-1 [27.2 kB]
Get:7 http://deb.debian.org/debian trixie/main amd64 media-types all 13.0.0 [29.3 kB]
Get:8 http://deb.debian.org/debian trixie/main amd64 libpython3.13-stdlib amd64 3.13.5-2 [1956 kB]
Get:9 http://deb.debian.org/debian trixie/main amd64 python3.13 amd64 3.13.5-2 [757 kB]
Get:10 http://deb.debian.org/debian trixie/main amd64 libpython3-stdlib amd64 3.13.5-1 [10.2 kB]
Get:11 http://deb.debian.org/debian trixie/main amd64 python3 amd64 3.13.5-1 [28.2 kB]
Get:12 http://deb.debian.org/debian trixie/main amd64 python3-numpy-dev amd64 1:2.2.4+ds-1 [139 kB]
Get:13 http://deb.debian.org/debian trixie/main amd64 libblas3 amd64 3.12.1-6 [160 kB]
Get:14 http://deb.debian.org/debian trixie/main amd64 libgfortran5 amd64 14.2.0-19 [836 kB]
Get:15 http://deb.debian.org/debian trixie/main amd64 liblapack3 amd64 3.12.1-6 [2447 kB]
Get:16 http://deb.debian.org/debian trixie/main amd64 python3-numpy amd64 1:2.2.4+ds-1 [5096 kB]
Get:17 http://deb.debian.org/debian trixie/main amd64 libpcre2-8-0 amd64 10.46-1~deb13u1 [299 kB]
Get:18 http://deb.debian.org/debian trixie/main amd64 less amd64 668-1 [161 kB]
Get:19 http://deb.debian.org/debian trixie/main amd64 bash-completion all 1:2.16.0-7 [319 kB]
Get:20 http://deb.debian.org/debian trixie/main amd64 bzip2 amd64 1.0.8-6 [40.5 kB]
Get:21 http://deb.debian.org/debian trixie/main amd64 libmagic-mgc amd64 1:5.46-5 [338 kB]
Get:22 http://deb.debian.org/debian trixie/main amd64 libmagic1t64 amd64 1:5.46-5 [109 kB]
Get:23 http://deb.debian.org/debian trixie/main amd64 file amd64 1:5.46-5 [43.6 kB]
Get:24 http://deb.debian.org/debian trixie/main amd64 krb5-locales all 1.21.3-5 [101 kB]
Get:25 http://deb.debian.org/debian trixie/main amd64 manpages all 6.9.1-1 [1393 kB]
Get:26 http://deb.debian.org/debian trixie/main amd64 libedit2 amd64 3.1-20250104-1 [93.8 kB]
Get:27 http://deb.debian.org/debian trixie/main amd64 libcbor0.10 amd64 0.10.2-2 [28.3 kB]
Get:28 http://deb.debian.org/debian trixie/main amd64 libfido2-1 amd64 1.15.0-1+b1 [78.7 kB]
Get:29 http://deb.debian.org/debian trixie/main amd64 libkrb5support0 amd64 1.21.3-5 [33.0 kB]
Get:30 http://deb.debian.org/debian trixie/main amd64 libcom-err2 amd64 1.47.2-3+b3 [25.0 kB]
Get:31 http://deb.debian.org/debian trixie/main amd64 libk5crypto3 amd64 1.21.3-5 [81.5 kB]
Get:32 http://deb.debian.org/debian trixie/main amd64 libkeyutils1 amd64 1.6.3-6 [9456 B]
Get:33 http://deb.debian.org/debian trixie/main amd64 libkrb5-3 amd64 1.21.3-5 [326 kB]
Get:34 http://deb.debian.org/debian trixie/main amd64 libgssapi-krb5-2 amd64 1.21.3-5 [138 kB]
Get:35 http://deb.debian.org/debian trixie/main amd64 openssh-client amd64 1:10.0p1-7 [985 kB]
Get:36 http://deb.debian.org/debian trixie/main amd64 perl-modules-5.40 all 5.40.1-6 [3019 kB]
Get:37 http://deb.debian.org/debian trixie/main amd64 libgdbm-compat4t64 amd64 1.24-2 [50.3 kB]
Get:38 http://deb.debian.org/debian trixie/main amd64 libperl5.40 amd64 5.40.1-6 [4341 kB]
Get:39 http://deb.debian.org/debian trixie/main amd64 perl amd64 5.40.1-6 [267 kB]
Get:40 http://deb.debian.org/debian trixie/main amd64 libunistring5 amd64 1.3-2 [477 kB]
Get:41 http://deb.debian.org/debian trixie/main amd64 libidn2-0 amd64 2.3.8-2 [109 kB]
Get:42 http://deb.debian.org/debian trixie/main amd64 libp11-kit0 amd64 0.25.5-3 [425 kB]
Get:43 http://deb.debian.org/debian trixie/main amd64 libtasn1-6 amd64 4.20.0-2 [49.9 kB]
Get:44 http://deb.debian.org/debian trixie/main amd64 libgnutls30t64 amd64 3.8.9-3 [1465 kB]
Get:45 http://deb.debian.org/debian trixie/main amd64 libpsl5t64 amd64 0.21.2-1.1+b1 [57.2 kB]
Get:46 http://deb.debian.org/debian trixie/main amd64 wget amd64 1.25.0-2 [984 kB]
Get:47 http://deb.debian.org/debian trixie/main amd64 xz-utils amd64 5.8.1-1 [660 kB]
Get:48 http://deb.debian.org/debian trixie/main amd64 m4 amd64 1.4.19-8 [294 kB]
Get:49 http://deb.debian.org/debian trixie/main amd64 autoconf all 2.72-3.1 [494 kB]
Get:50 http://deb.debian.org/debian trixie/main amd64 autotools-dev all 20240727.1 [60.2 kB]
Get:51 http://deb.debian.org/debian trixie/main amd64 automake all 1:1.17-4 [862 kB]
Get:52 http://deb.debian.org/debian trixie/main amd64 libsframe1 amd64 2.44-3 [78.4 kB]
Get:53 http://deb.debian.org/debian trixie/main amd64 binutils-common amd64 2.44-3 [2509 kB]
Get:54 http://deb.debian.org/debian trixie/main amd64 libbinutils amd64 2.44-3 [534 kB]
Get:55 http://deb.debian.org/debian trixie/main amd64 libgprofng0 amd64 2.44-3 [808 kB]
Get:56 http://deb.debian.org/debian trixie/main amd64 libctf-nobfd0 amd64 2.44-3 [156 kB]
Get:57 http://deb.debian.org/debian trixie/main amd64 libctf0 amd64 2.44-3 [88.6 kB]
Get:58 http://deb.debian.org/debian trixie/main amd64 libjansson4 amd64 2.14-2+b3 [39.8 kB]
Get:59 http://deb.debian.org/debian trixie/main amd64 binutils-x86-64-linux-gnu amd64 2.44-3 [1014 kB]
Get:60 http://deb.debian.org/debian trixie/main amd64 binutils amd64 2.44-3 [265 kB]
Get:61 http://deb.debian.org/debian trixie/main amd64 libc-dev-bin amd64 2.41-12 [58.2 kB]
Get:62 http://deb.debian.org/debian trixie/main amd64 linux-libc-dev all 6.12.43-1 [2654 kB]
Get:63 http://deb.debian.org/debian trixie/main amd64 libcrypt-dev amd64 1:4.4.38-1 [119 kB]
Get:64 http://deb.debian.org/debian trixie/main amd64 rpcsvc-proto amd64 1.4.3-1 [63.3 kB]
Get:65 http://deb.debian.org/debian trixie/main amd64 libc6-dev amd64 2.41-12 [1991 kB]
Get:66 http://deb.debian.org/debian trixie/main amd64 libisl23 amd64 0.27-1 [659 kB]
Get:67 http://deb.debian.org/debian trixie/main amd64 libmpfr6 amd64 4.2.2-1 [729 kB]
Get:68 http://deb.debian.org/debian trixie/main amd64 libmpc3 amd64 1.3.1-1+b3 [52.2 kB]
Get:69 http://deb.debian.org/debian trixie/main amd64 cpp-14-x86-64-linux-gnu amd64 14.2.0-19 [11.0 MB]
Get:70 http://deb.debian.org/debian trixie/main amd64 cpp-14 amd64 14.2.0-19 [1280 B]
Get:71 http://deb.debian.org/debian trixie/main amd64 cpp-x86-64-linux-gnu amd64 4:14.2.0-1 [4840 B]
Get:72 http://deb.debian.org/debian trixie/main amd64 cpp amd64 4:14.2.0-1 [1568 B]
Get:73 http://deb.debian.org/debian trixie/main amd64 libcc1-0 amd64 14.2.0-19 [42.8 kB]
Get:74 http://deb.debian.org/debian trixie/main amd64 libgomp1 amd64 14.2.0-19 [137 kB]
Get:75 http://deb.debian.org/debian trixie/main amd64 libitm1 amd64 14.2.0-19 [26.0 kB]
Get:76 http://deb.debian.org/debian trixie/main amd64 libatomic1 amd64 14.2.0-19 [9308 B]
Get:77 http://deb.debian.org/debian trixie/main amd64 libasan8 amd64 14.2.0-19 [2725 kB]
Get:78 http://deb.debian.org/debian trixie/main amd64 liblsan0 amd64 14.2.0-19 [1204 kB]
Get:79 http://deb.debian.org/debian trixie/main amd64 libtsan2 amd64 14.2.0-19 [2460 kB]
Get:80 http://deb.debian.org/debian trixie/main amd64 libubsan1 amd64 14.2.0-19 [1074 kB]
Get:81 http://deb.debian.org/debian trixie/main amd64 libhwasan0 amd64 14.2.0-19 [1488 kB]
Get:82 http://deb.debian.org/debian trixie/main amd64 libquadmath0 amd64 14.2.0-19 [145 kB]
Get:83 http://deb.debian.org/debian trixie/main amd64 libgcc-14-dev amd64 14.2.0-19 [2672 kB]
Get:84 http://deb.debian.org/debian trixie/main amd64 gcc-14-x86-64-linux-gnu amd64 14.2.0-19 [21.4 MB]
Get:85 http://deb.debian.org/debian trixie/main amd64 gcc-14 amd64 14.2.0-19 [540 kB]
Get:86 http://deb.debian.org/debian trixie/main amd64 gcc-x86-64-linux-gnu amd64 4:14.2.0-1 [1436 B]
Get:87 http://deb.debian.org/debian trixie/main amd64 gcc amd64 4:14.2.0-1 [5136 B]
Get:88 http://deb.debian.org/debian trixie/main amd64 libstdc++-14-dev amd64 14.2.0-19 [2376 kB]
Get:89 http://deb.debian.org/debian trixie/main amd64 g++-14-x86-64-linux-gnu amd64 14.2.0-19 [12.1 MB]
Get:90 http://deb.debian.org/debian trixie/main amd64 g++-14 amd64 14.2.0-19 [22.5 kB]
Get:91 http://deb.debian.org/debian trixie/main amd64 g++-x86-64-linux-gnu amd64 4:14.2.0-1 [1200 B]
Get:92 http://deb.debian.org/debian trixie/main amd64 g++ amd64 4:14.2.0-1 [1344 B]
Get:93 http://deb.debian.org/debian trixie/main amd64 make amd64 4.4.1-2 [463 kB]
Get:94 http://deb.debian.org/debian trixie/main amd64 libdpkg-perl all 1.22.21 [650 kB]
Get:95 http://deb.debian.org/debian trixie/main amd64 patch amd64 2.8-2 [134 kB]
Get:96 http://deb.debian.org/debian trixie/main amd64 dpkg-dev all 1.22.21 [1338 kB]
Get:97 http://deb.debian.org/debian trixie/main amd64 build-essential amd64 12.12 [4624 B]
Get:98 http://deb.debian.org/debian trixie/main amd64 cfortran all 20210827-1.1 [64.6 kB]
Get:99 http://deb.debian.org/debian trixie/main amd64 comerr-dev amd64 2.1-1.47.2-3+b3 [56.7 kB]
Get:100 http://deb.debian.org/debian trixie/main amd64 libbrotli1 amd64 1.1.0-2+b7 [307 kB]
Get:101 http://deb.debian.org/debian trixie/main amd64 libsasl2-modules-db amd64 2.1.28+dfsg1-9 [19.8 kB]
Get:102 http://deb.debian.org/debian trixie/main amd64 libsasl2-2 amd64 2.1.28+dfsg1-9 [57.5 kB]
Get:103 http://deb.debian.org/debian trixie/main amd64 libldap2 amd64 2.6.10+dfsg-1 [194 kB]
Get:104 http://deb.debian.org/debian trixie/main amd64 libnghttp2-14 amd64 1.64.0-1.1 [76.0 kB]
Get:105 http://deb.debian.org/debian trixie/main amd64 libnghttp3-9 amd64 1.8.0-1 [67.7 kB]
Get:106 http://deb.debian.org/debian trixie/main amd64 librtmp1 amd64 2.4+20151223.gitfa8646d.1-2+b5 [58.8 kB]
Get:107 http://deb.debian.org/debian trixie/main amd64 libssh2-1t64 amd64 1.11.1-1 [245 kB]
Get:108 http://deb.debian.org/debian trixie/main amd64 libcurl4t64 amd64 8.14.1-2 [391 kB]
Get:109 http://deb.debian.org/debian trixie/main amd64 curl amd64 8.14.1-2 [269 kB]
Get:110 http://deb.debian.org/debian trixie/main amd64 mysql-common all 5.8+1.1.1 [6784 B]
Get:111 http://deb.debian.org/debian trixie/main amd64 mariadb-common all 1:11.8.3-0+deb13u1 [28.8 kB]
Get:112 http://deb.debian.org/debian trixie/main amd64 libmariadb3 amd64 1:11.8.3-0+deb13u1 [187 kB]
Get:113 http://deb.debian.org/debian trixie/main amd64 libssl-dev amd64 3.5.1-1 [2954 kB]
Get:114 http://deb.debian.org/debian trixie/main amd64 zlib1g-dev amd64 1:1.3.dfsg+really1.3.1-1+b1 [920 kB]
Get:115 http://deb.debian.org/debian trixie/main amd64 libmariadb-dev amd64 1:11.8.3-0+deb13u1 [277 kB]
Get:116 http://deb.debian.org/debian trixie/main amd64 libmariadb-dev-compat amd64 1:11.8.3-0+deb13u1 [27.5 kB]
Get:117 http://deb.debian.org/debian trixie/main amd64 default-libmysqlclient-dev amd64 1.1.1 [3252 B]
Get:118 http://deb.debian.org/debian trixie/main amd64 libgpg-error0 amd64 1.51-4 [82.1 kB]
Get:119 http://deb.debian.org/debian trixie/main amd64 libassuan9 amd64 3.0.2-2 [61.5 kB]
Get:120 http://deb.debian.org/debian trixie/main amd64 libgcrypt20 amd64 1.11.0-7 [843 kB]
Get:121 http://deb.debian.org/debian trixie/main amd64 gpgconf amd64 2.4.7-21+b3 [129 kB]
Get:122 http://deb.debian.org/debian trixie/main amd64 libksba8 amd64 1.6.7-2+b1 [136 kB]
Get:123 http://deb.debian.org/debian trixie/main amd64 libnpth0t64 amd64 1.8-3 [23.2 kB]
Get:124 http://deb.debian.org/debian trixie/main amd64 dirmngr amd64 2.4.7-21+b3 [384 kB]
Get:125 http://deb.debian.org/debian trixie/main amd64 libfakeroot amd64 ********-1 [29.6 kB]
Get:126 http://deb.debian.org/debian trixie/main amd64 fakeroot amd64 ********-1 [76.0 kB]
Get:127 http://deb.debian.org/debian trixie/main amd64 fonts-dejavu-mono all 2.37-8 [489 kB]
Get:128 http://deb.debian.org/debian trixie/main amd64 fonts-dejavu-core all 2.37-8 [840 kB]
Get:129 http://deb.debian.org/debian trixie/main amd64 fontconfig-config amd64 2.15.0-2.3 [318 kB]
Get:130 http://deb.debian.org/debian trixie/main amd64 gdal-data all 3.10.3+dfsg-1 [403 kB]
Get:131 http://deb.debian.org/debian trixie/main amd64 gdal-plugins amd64 3.10.3+dfsg-1 [181 kB]
Get:132 http://deb.debian.org/debian trixie/main amd64 libaec0 amd64 1.1.3-1+b1 [23.8 kB]
Get:133 http://deb.debian.org/debian trixie/main amd64 libarpack2t64 amd64 3.9.1-6 [104 kB]
Get:134 http://deb.debian.org/debian trixie/main amd64 libarmadillo14 amd64 1:14.2.3+dfsg-1+b1 [102 kB]
Get:135 http://deb.debian.org/debian trixie/main amd64 libaom3 amd64 3.12.1-1 [1871 kB]
Get:136 http://deb.debian.org/debian trixie/main amd64 libdav1d7 amd64 1.5.1-1 [559 kB]
Get:137 http://deb.debian.org/debian trixie/main amd64 libabsl20240722 amd64 20240722.0-4 [492 kB]
Get:138 http://deb.debian.org/debian trixie/main amd64 libgav1-1 amd64 0.19.0-3+b1 [353 kB]
Get:139 http://deb.debian.org/debian trixie/main amd64 librav1e0.7 amd64 0.7.1-9+b2 [946 kB]
Get:140 http://deb.debian.org/debian trixie/main amd64 libsvtav1enc2 amd64 2.3.0+dfsg-1 [2489 kB]
Get:141 http://deb.debian.org/debian trixie/main amd64 libjpeg62-turbo amd64 1:2.1.5-4 [168 kB]
Get:142 http://deb.debian.org/debian trixie/main amd64 libyuv0 amd64 0.0.1904.20250204-1 [174 kB]
Get:143 http://deb.debian.org/debian trixie/main amd64 libavif16 amd64 1.2.1-1.2 [133 kB]
Get:144 http://deb.debian.org/debian trixie/main amd64 libsnappy1v5 amd64 1.2.2-1 [29.3 kB]
Get:145 http://deb.debian.org/debian trixie/main amd64 libblosc1 amd64 1.21.5+ds-1+b2 [49.5 kB]
Get:146 http://deb.debian.org/debian trixie/main amd64 libngtcp2-16 amd64 1.11.0-1 [131 kB]
Get:147 http://deb.debian.org/debian trixie/main amd64 libngtcp2-crypto-gnutls8 amd64 1.11.0-1 [29.3 kB]
Get:148 http://deb.debian.org/debian trixie/main amd64 libcurl3t64-gnutls amd64 8.14.1-2 [384 kB]
Get:149 http://deb.debian.org/debian trixie/main amd64 libcfitsio10t64 amd64 4.6.2-2 [577 kB]
Get:150 http://deb.debian.org/debian trixie/main amd64 libdeflate0 amd64 1.23-2 [47.3 kB]
Get:151 http://deb.debian.org/debian trixie/main amd64 libminizip1t64 amd64 1:1.3.dfsg+really1.3.1-1+b1 [53.3 kB]
Get:152 http://deb.debian.org/debian trixie/main amd64 libfreexl1 amd64 2.0.0-1+b3 [40.0 kB]
Get:153 http://deb.debian.org/debian trixie/main amd64 libfyba0t64 amd64 4.1.1-11+b1 [113 kB]
Get:154 http://deb.debian.org/debian trixie/main amd64 libgeos3.13.1 amd64 3.13.1-1 [970 kB]
Get:155 http://deb.debian.org/debian trixie/main amd64 libgeos-c1t64 amd64 3.13.1-1 [102 kB]
Get:156 http://deb.debian.org/debian trixie/main amd64 proj-data all 9.6.0-1 [6303 kB]
Get:157 http://deb.debian.org/debian trixie/main amd64 libjbig0 amd64 2.1-6.1+b2 [32.1 kB]
Get:158 http://deb.debian.org/debian trixie/main amd64 liblerc4 amd64 4.0.0+ds-5 [183 kB]
Get:159 http://deb.debian.org/debian trixie/main amd64 libsharpyuv0 amd64 1.5.0-0.1 [116 kB]
Get:160 http://deb.debian.org/debian trixie/main amd64 libwebp7 amd64 1.5.0-0.1 [318 kB]
Get:161 http://deb.debian.org/debian trixie/main amd64 libtiff6 amd64 4.7.0-3 [346 kB]
Get:162 http://deb.debian.org/debian trixie/main amd64 libproj25 amd64 9.6.0-1 [1453 kB]
Get:163 http://deb.debian.org/debian trixie/main amd64 libgeotiff5 amd64 1.7.4-1 [68.3 kB]
Get:164 http://deb.debian.org/debian trixie/main amd64 libgif7 amd64 5.2.2-1+b1 [44.2 kB]
Get:165 http://deb.debian.org/debian trixie/main amd64 libsz2 amd64 1.1.3-1+b1 [8080 B]
Get:166 http://deb.debian.org/debian trixie/main amd64 libhdf4-0-alt amd64 4.3.0-1+b1 [269 kB]
Get:167 http://deb.debian.org/debian trixie/main amd64 libhdf5-310 amd64 1.14.5+repack-3 [1344 kB]
Get:168 http://deb.debian.org/debian trixie/main amd64 libheif-plugin-dav1d amd64 1.19.8-1 [11.7 kB]
Get:169 http://deb.debian.org/debian trixie/main amd64 libde265-0 amd64 1.0.15-1+b3 [189 kB]
Get:170 http://deb.debian.org/debian trixie/main amd64 libheif-plugin-libde265 amd64 1.19.8-1 [15.3 kB]
Get:171 http://deb.debian.org/debian trixie/main amd64 libheif1 amd64 1.19.8-1 [520 kB]
Get:172 http://deb.debian.org/debian trixie/main amd64 libjson-c5 amd64 0.18+ds-1 [46.0 kB]
Get:173 http://deb.debian.org/debian trixie/main amd64 liburiparser1 amd64 0.9.8+dfsg-2 [45.5 kB]
Get:174 http://deb.debian.org/debian trixie/main amd64 libkmlbase1t64 amd64 1.3.0-12+b2 [50.5 kB]
Get:175 http://deb.debian.org/debian trixie/main amd64 libkmldom1t64 amd64 1.3.0-12+b2 [158 kB]
Get:176 http://deb.debian.org/debian trixie/main amd64 libkmlengine1t64 amd64 1.3.0-12+b2 [80.1 kB]
Get:177 http://deb.debian.org/debian trixie/main amd64 libhdf5-hl-310 amd64 1.14.5+repack-3 [69.3 kB]
Get:178 http://deb.debian.org/debian trixie/main amd64 libxml2 amd64 2.12.7+dfsg+really2.9.14-2.1+deb13u1 [698 kB]
Get:179 http://deb.debian.org/debian trixie/main amd64 libnetcdf22 amd64 1:4.9.3-1 [517 kB]
Get:180 http://deb.debian.org/debian trixie/main amd64 libltdl7 amd64 2.5.4-4 [416 kB]
Get:181 http://deb.debian.org/debian trixie/main amd64 libodbc2 amd64 2.3.12-2 [151 kB]
Get:182 http://deb.debian.org/debian trixie/main amd64 unixodbc-common all 2.3.12-2 [8640 B]
Get:183 http://deb.debian.org/debian trixie/main amd64 libodbcinst2 amd64 2.3.12-2 [35.1 kB]
Get:184 http://deb.debian.org/debian trixie/main amd64 libtirpc-common all 1.3.6+ds-1 [11.0 kB]
Get:185 http://deb.debian.org/debian trixie/main amd64 libtirpc3t64 amd64 1.3.6+ds-1 [83.3 kB]
Get:186 http://deb.debian.org/debian trixie/main amd64 libogdi4.1 amd64 4.1.1+ds-5 [217 kB]
Get:187 http://deb.debian.org/debian trixie/main amd64 libopenjp2-7 amd64 2.5.3-2.1~deb13u1 [205 kB]
Get:188 http://deb.debian.org/debian trixie/main amd64 libpng16-16t64 amd64 1.6.48-1 [282 kB]
Get:189 http://deb.debian.org/debian trixie/main amd64 libfreetype6 amd64 2.13.3+dfsg-1 [452 kB]
Get:190 http://deb.debian.org/debian trixie/main amd64 libfontconfig1 amd64 2.15.0-2.3 [392 kB]
Get:191 http://deb.debian.org/debian trixie/main amd64 gnupg-l10n all 2.4.7-21 [747 kB]
Get:192 http://deb.debian.org/debian trixie/main amd64 gpg amd64 2.4.7-21+b3 [634 kB]
Get:193 http://deb.debian.org/debian trixie/main amd64 pinentry-curses amd64 1.3.1-2 [86.4 kB]
Get:194 http://deb.debian.org/debian trixie/main amd64 gpg-agent amd64 2.4.7-21+b3 [271 kB]
Get:195 http://deb.debian.org/debian trixie/main amd64 gpgsm amd64 2.4.7-21+b3 [275 kB]
Get:196 http://deb.debian.org/debian trixie/main amd64 gnupg all 2.4.7-21 [417 kB]
Get:197 http://deb.debian.org/debian trixie/main amd64 libgpgme11t64 amd64 1.24.2-3 [346 kB]
Get:198 http://deb.debian.org/debian trixie/main amd64 libgpgmepp6t64 amd64 1.24.2-3 [341 kB]
Get:199 http://deb.debian.org/debian trixie/main amd64 liblcms2-2 amd64 2.16-2 [160 kB]
Get:200 http://deb.debian.org/debian trixie/main amd64 libnspr4 amd64 2:4.36-1 [110 kB]
Get:201 http://deb.debian.org/debian trixie/main amd64 libnss3 amd64 2:3.110-1 [1393 kB]
Get:202 http://deb.debian.org/debian trixie/main amd64 libpoppler147 amd64 25.03.0-5 [2034 kB]
Get:203 http://deb.debian.org/debian trixie/main amd64 libpq5 amd64 17.6-0+deb13u1 [228 kB]
Get:204 http://deb.debian.org/debian trixie/main amd64 libqhull-r8.0 amd64 2020.2-6+b2 [248 kB]
Get:205 http://deb.debian.org/debian trixie/main amd64 librttopo1 amd64 1.1.0-4 [179 kB]
Get:206 http://deb.debian.org/debian trixie/main amd64 libspatialite8t64 amd64 5.1.0-3+b2 [1864 kB]
Get:207 http://deb.debian.org/debian trixie/main amd64 libicu76 amd64 76.1-4 [9722 kB]
Get:208 http://deb.debian.org/debian trixie/main amd64 libxerces-c3.2t64 amd64 3.2.4+debian-1.3+b2 [898 kB]
Get:209 http://deb.debian.org/debian trixie/main amd64 libgdal36 amd64 3.10.3+dfsg-1 [9260 kB]
Get:210 http://deb.debian.org/debian trixie/main amd64 python3-gdal amd64 3.10.3+dfsg-1 [959 kB]
Get:211 http://deb.debian.org/debian trixie/main amd64 gdal-bin amd64 3.10.3+dfsg-1 [759 kB]
Get:212 http://deb.debian.org/debian trixie/main amd64 libgfortran-14-dev amd64 14.2.0-19 [880 kB]
Get:213 http://deb.debian.org/debian trixie/main amd64 gfortran-14-x86-64-linux-gnu amd64 14.2.0-19 [11.7 MB]
Get:214 http://deb.debian.org/debian trixie/main amd64 gfortran-14 amd64 14.2.0-19 [14.5 kB]
Get:215 http://deb.debian.org/debian trixie/main amd64 gfortran-x86-64-linux-gnu amd64 4:14.2.0-1 [1284 B]
Get:216 http://deb.debian.org/debian trixie/main amd64 gfortran amd64 4:14.2.0-1 [1436 B]
Get:217 http://deb.debian.org/debian trixie/main amd64 liberror-perl all 0.17030-1 [26.9 kB]
Get:218 http://deb.debian.org/debian trixie/main amd64 git-man all 1:2.47.3-0+deb13u1 [2205 kB]
Get:219 http://deb.debian.org/debian trixie/main amd64 git amd64 1:2.47.3-0+deb13u1 [8862 kB]
Get:220 http://deb.debian.org/debian trixie/main amd64 gpg-wks-client amd64 2.4.7-21+b3 [108 kB]
Get:221 http://deb.debian.org/debian trixie/main amd64 gpgv amd64 2.4.7-21+b3 [241 kB]
Get:222 http://deb.debian.org/debian trixie/main amd64 hdf5-helpers amd64 1.14.5+repack-3 [26.6 kB]
Get:223 http://deb.debian.org/debian trixie/main amd64 icu-devtools amd64 76.1-4 [215 kB]
Get:224 http://deb.debian.org/debian trixie/main amd64 libgssrpc4t64 amd64 1.21.3-5 [60.0 kB]
Get:225 http://deb.debian.org/debian trixie/main amd64 libkadm5clnt-mit12 amd64 1.21.3-5 [42.5 kB]
Get:226 http://deb.debian.org/debian trixie/main amd64 libkdb5-10t64 amd64 1.21.3-5 [42.6 kB]
Get:227 http://deb.debian.org/debian trixie/main amd64 libkadm5srv-mit12 amd64 1.21.3-5 [54.2 kB]
Get:228 http://deb.debian.org/debian trixie/main amd64 krb5-multidev amd64 1.21.3-5 [126 kB]
Get:229 http://deb.debian.org/debian trixie/main amd64 libaec-dev amd64 1.1.3-1+b1 [21.5 kB]
Get:230 http://deb.debian.org/debian trixie/main amd64 libalgorithm-diff-perl all 1.201-1 [43.3 kB]
Get:231 http://deb.debian.org/debian trixie/main amd64 libalgorithm-diff-xs-perl amd64 0.04-9 [11.1 kB]
Get:232 http://deb.debian.org/debian trixie/main amd64 libalgorithm-merge-perl all 0.08-5 [11.8 kB]
Get:233 http://deb.debian.org/debian trixie/main amd64 libaom-dev amd64 3.12.1-1 [2122 kB]
Get:234 http://deb.debian.org/debian trixie/main amd64 libblas-dev amd64 3.12.1-6 [170 kB]
Get:235 http://deb.debian.org/debian trixie/main amd64 liblapack-dev amd64 3.12.1-6 [4991 kB]
Get:236 http://deb.debian.org/debian trixie/main amd64 libarpack2-dev amd64 3.9.1-6 [120 kB]
Get:237 http://deb.debian.org/debian trixie/main amd64 libhdf5-fortran-310 amd64 1.14.5+repack-3 [111 kB]
Get:238 http://deb.debian.org/debian trixie/main amd64 libhdf5-hl-fortran-310 amd64 1.14.5+repack-3 [43.9 kB]
Get:239 http://deb.debian.org/debian trixie/main amd64 libhdf5-cpp-310 amd64 1.14.5+repack-3 [133 kB]
Get:240 http://deb.debian.org/debian trixie/main amd64 libhdf5-hl-cpp-310 amd64 1.14.5+repack-3 [24.4 kB]
Get:241 http://deb.debian.org/debian trixie/main amd64 libjpeg62-turbo-dev amd64 1:2.1.5-4 [292 kB]
Get:242 http://deb.debian.org/debian trixie/main amd64 libjpeg-dev amd64 1:2.1.5-4 [72.2 kB]
Get:243 http://deb.debian.org/debian trixie/main amd64 libbrotli-dev amd64 1.1.0-2+b7 [316 kB]
Get:244 http://deb.debian.org/debian trixie/main amd64 libidn2-dev amd64 2.3.8-2 [103 kB]
Get:245 http://deb.debian.org/debian trixie/main amd64 libkrb5-dev amd64 1.21.3-5 [16.1 kB]
Get:246 http://deb.debian.org/debian trixie/main amd64 libldap-dev amd64 2.6.10+dfsg-1 [312 kB]
Get:247 http://deb.debian.org/debian trixie/main amd64 libpkgconf3 amd64 1.8.1-4 [36.4 kB]
Get:248 http://deb.debian.org/debian trixie/main amd64 pkgconf-bin amd64 1.8.1-4 [30.2 kB]
Get:249 http://deb.debian.org/debian trixie/main amd64 pkgconf amd64 1.8.1-4 [26.2 kB]
Get:250 http://deb.debian.org/debian trixie/main amd64 libnghttp2-dev amd64 1.64.0-1.1 [116 kB]
Get:251 http://deb.debian.org/debian trixie/main amd64 libnghttp3-dev amd64 1.8.0-1 [91.3 kB]
Get:252 http://deb.debian.org/debian trixie/main amd64 libpsl-dev amd64 0.21.2-1.1+b1 [77.6 kB]
Get:253 http://deb.debian.org/debian trixie/main amd64 libgmpxx4ldbl amd64 2:6.3.0+dfsg-3 [329 kB]
Get:254 http://deb.debian.org/debian trixie/main amd64 libgmp-dev amd64 2:6.3.0+dfsg-3 [642 kB]
Get:255 http://deb.debian.org/debian trixie/main amd64 libevent-2.1-7t64 amd64 2.1.12-stable-10+b1 [182 kB]
Get:256 http://deb.debian.org/debian trixie/main amd64 libunbound8 amd64 1.22.0-2 [598 kB]
Get:257 http://deb.debian.org/debian trixie/main amd64 libgnutls-dane0t64 amd64 3.8.9-3 [455 kB]
Get:258 http://deb.debian.org/debian trixie/main amd64 libgnutls-openssl27t64 amd64 3.8.9-3 [455 kB]
Get:259 http://deb.debian.org/debian trixie/main amd64 libp11-kit-dev amd64 0.25.5-3 [208 kB]
Get:260 http://deb.debian.org/debian trixie/main amd64 libtasn1-6-dev amd64 4.20.0-2 [99.2 kB]
Get:261 http://deb.debian.org/debian trixie/main amd64 nettle-dev amd64 3.10.1-1 [1318 kB]
Get:262 http://deb.debian.org/debian trixie/main amd64 libgnutls28-dev amd64 3.8.9-3 [1406 kB]
Get:263 http://deb.debian.org/debian trixie/main amd64 librtmp-dev amd64 2.4+20151223.gitfa8646d.1-2+b5 [68.0 kB]
Get:264 http://deb.debian.org/debian trixie/main amd64 libssh2-1-dev amd64 1.11.1-1 [393 kB]
Get:265 http://deb.debian.org/debian trixie/main amd64 libzstd-dev amd64 1.5.7+dfsg-1 [371 kB]
Get:266 http://deb.debian.org/debian trixie/main amd64 libcurl4-openssl-dev amd64 8.14.1-2 [510 kB]
Get:267 http://deb.debian.org/debian trixie/main amd64 libhdf5-dev amd64 1.14.5+repack-3 [3165 kB]
Get:268 http://deb.debian.org/debian trixie/main amd64 libsuperlu7 amd64 7.0.1+dfsg1-2 [174 kB]
Get:269 http://deb.debian.org/debian trixie/main amd64 libsuperlu-dev amd64 7.0.1+dfsg1-2 [22.0 kB]
Get:270 http://deb.debian.org/debian trixie/main amd64 libarmadillo-dev amd64 1:14.2.3+dfsg-1+b1 [414 kB]
Get:271 http://deb.debian.org/debian trixie/main amd64 libdav1d-dev amd64 1.5.1-1 [26.6 kB]
Get:272 http://deb.debian.org/debian trixie/main amd64 librav1e-dev amd64 0.7.1-9+b2 [14.4 kB]
Get:273 http://deb.debian.org/debian trixie/main amd64 libsvtav1enc-dev amd64 2.3.0+dfsg-1 [33.0 kB]
Get:274 http://deb.debian.org/debian trixie/main amd64 libyuv-dev amd64 0.0.1904.20250204-1 [226 kB]
Get:275 http://deb.debian.org/debian trixie/main amd64 libavif-dev amd64 1.2.1-1.2 [59.0 kB]
Get:276 http://deb.debian.org/debian trixie/main amd64 libblosc-dev amd64 1.21.5+ds-1+b2 [61.5 kB]
Get:277 http://deb.debian.org/debian trixie/main amd64 libboost1.83-dev amd64 1.83.0-4.2 [10.6 MB]
Get:278 http://deb.debian.org/debian trixie/main amd64 libboost-dev amd64 1.83.0.2+b2 [3836 B]
Get:279 http://deb.debian.org/debian trixie/main amd64 libcfitsio-dev amd64 4.6.2-2 [642 kB]
Get:280 http://deb.debian.org/debian trixie/main amd64 libcfitsio-doc all 4.6.2-2 [2153 kB]
Get:281 http://deb.debian.org/debian trixie/main amd64 libdeflate-dev amd64 1.23-2 [55.9 kB]
Get:282 http://deb.debian.org/debian trixie/main amd64 libexpat1-dev amd64 2.7.1-2 [161 kB]
Get:283 http://deb.debian.org/debian trixie/main amd64 libfile-fcntllock-perl amd64 0.22-4+b4 [34.6 kB]
Get:284 http://deb.debian.org/debian trixie/main amd64 libminizip-dev amd64 1:1.3.dfsg+really1.3.1-1+b1 [61.0 kB]
Get:285 http://deb.debian.org/debian trixie/main amd64 libfreexl-dev amd64 2.0.0-1+b3 [42.1 kB]
Get:286 http://deb.debian.org/debian trixie/main amd64 libfyba-dev amd64 4.1.1-11+b1 [169 kB]
Get:287 http://deb.debian.org/debian trixie/main amd64 libgeos-dev amd64 3.13.1-1 [58.6 kB]
Get:288 http://deb.debian.org/debian trixie/main amd64 libsqlite3-dev amd64 3.46.1-7 [1109 kB]
Get:289 http://deb.debian.org/debian trixie/main amd64 libjbig-dev amd64 2.1-6.1+b2 [31.4 kB]
Get:290 http://deb.debian.org/debian trixie/main amd64 liblzma-dev amd64 5.8.1-1 [348 kB]
Get:291 http://deb.debian.org/debian trixie/main amd64 libwebpdemux2 amd64 1.5.0-0.1 [113 kB]
Get:292 http://deb.debian.org/debian trixie/main amd64 libwebpmux3 amd64 1.5.0-0.1 [126 kB]
Get:293 http://deb.debian.org/debian trixie/main amd64 libwebpdecoder3 amd64 1.5.0-0.1 [208 kB]
Get:294 http://deb.debian.org/debian trixie/main amd64 libsharpyuv-dev amd64 1.5.0-0.1 [120 kB]
Get:295 http://deb.debian.org/debian trixie/main amd64 libwebp-dev amd64 1.5.0-0.1 [450 kB]
Get:296 http://deb.debian.org/debian trixie/main amd64 libtiffxx6 amd64 4.7.0-3 [164 kB]
Get:297 http://deb.debian.org/debian trixie/main amd64 liblerc-dev amd64 4.0.0+ds-5 [183 kB]
Get:298 http://deb.debian.org/debian trixie/main amd64 libtiff-dev amd64 4.7.0-3 [488 kB]
Get:299 http://deb.debian.org/debian trixie/main amd64 libproj-dev amd64 9.6.0-1 [137 kB]
Get:300 http://deb.debian.org/debian trixie/main amd64 libgeotiff-dev amd64 1.7.4-1 [97.5 kB]
Get:301 http://deb.debian.org/debian trixie/main amd64 libgif-dev amd64 5.2.2-1+b1 [47.0 kB]
Get:302 http://deb.debian.org/debian trixie/main amd64 libxml2-dev amd64 2.12.7+dfsg+really2.9.14-2.1+deb13u1 [793 kB]
Get:303 http://deb.debian.org/debian trixie/main amd64 libnetcdf-dev amd64 1:4.9.3-1 [116 kB]
Get:304 http://deb.debian.org/debian trixie/main amd64 libhdf4-alt-dev amd64 4.3.0-1+b1 [324 kB]
Get:305 http://deb.debian.org/debian trixie/main amd64 libheif-dev amd64 1.19.8-1 [51.3 kB]
Get:306 http://deb.debian.org/debian trixie/main amd64 libjson-c-dev amd64 0.18+ds-1 [74.8 kB]
Get:307 http://deb.debian.org/debian trixie/main amd64 libkmlconvenience1t64 amd64 1.3.0-12+b2 [49.8 kB]
Get:308 http://deb.debian.org/debian trixie/main amd64 libkmlregionator1t64 amd64 1.3.0-12+b2 [22.8 kB]
Get:309 http://deb.debian.org/debian trixie/main amd64 libkmlxsd1t64 amd64 1.3.0-12+b2 [31.1 kB]
Get:310 http://deb.debian.org/debian trixie/main amd64 liburiparser-dev amd64 0.9.8+dfsg-2 [26.7 kB]
Get:311 http://deb.debian.org/debian trixie/main amd64 libkml-dev amd64 1.3.0-12+b2 [662 kB]
Get:312 http://deb.debian.org/debian trixie/main amd64 libltdl-dev amd64 2.5.4-4 [168 kB]
Get:313 http://deb.debian.org/debian trixie/main amd64 libxxhash-dev amd64 0.8.3-2 [81.0 kB]
Get:314 http://deb.debian.org/debian trixie/main amd64 liblz4-dev amd64 1.10.0-4 [88.4 kB]
Get:315 http://deb.debian.org/debian trixie/main amd64 libtirpc-dev amd64 1.3.6+ds-1 [191 kB]
Get:316 http://deb.debian.org/debian trixie/main amd64 libogdi-dev amd64 4.1.1+ds-5 [35.5 kB]
Get:317 http://deb.debian.org/debian trixie/main amd64 libopenjp2-7-dev amd64 2.5.3-2.1~deb13u1 [240 kB]
Get:318 http://deb.debian.org/debian trixie/main amd64 libpcre2-16-0 amd64 10.46-1~deb13u1 [281 kB]
Get:319 http://deb.debian.org/debian trixie/main amd64 libpcre2-32-0 amd64 10.46-1~deb13u1 [268 kB]
Get:320 http://deb.debian.org/debian trixie/main amd64 libpcre2-posix3 amd64 10.46-1~deb13u1 [63.9 kB]
Get:321 http://deb.debian.org/debian trixie/main amd64 libpcre2-dev amd64 10.46-1~deb13u1 [853 kB]
Get:322 http://deb.debian.org/debian trixie/main amd64 libpng-dev amd64 1.6.48-1 [366 kB]
Get:323 http://deb.debian.org/debian trixie/main amd64 libpoppler-dev amd64 25.03.0-5 [9308 B]
Get:324 http://deb.debian.org/debian trixie/main amd64 libpoppler-private-dev amd64 25.03.0-5 [182 kB]
Get:325 http://deb.debian.org/debian trixie/main amd64 libpq-dev amd64 17.6-0+deb13u1 [150 kB]
Get:326 http://deb.debian.org/debian trixie/main amd64 libqhull8.0 amd64 2020.2-6+b2 [246 kB]
Get:327 http://deb.debian.org/debian trixie/main amd64 libqhullcpp8.0 amd64 2020.2-6+b2 [108 kB]
Get:328 http://deb.debian.org/debian trixie/main amd64 libqhull-dev amd64 2020.2-6+b2 [522 kB]
Get:329 http://deb.debian.org/debian trixie/main amd64 librttopo-dev amd64 1.1.0-4 [220 kB]
Get:330 http://deb.debian.org/debian trixie/main amd64 libspatialite-dev amd64 5.1.0-3+b2 [2025 kB]
Get:331 http://deb.debian.org/debian trixie/main amd64 libicu-dev amd64 76.1-4 [10.8 MB]
Get:332 http://deb.debian.org/debian trixie/main amd64 libxerces-c-dev amd64 3.2.4+debian-1.3+b2 [1708 kB]
Get:333 http://deb.debian.org/debian trixie/main amd64 libodbccr2 amd64 2.3.12-2 [18.1 kB]
Get:334 http://deb.debian.org/debian trixie/main amd64 unixodbc-dev amd64 2.3.12-2 [234 kB]
Get:335 http://deb.debian.org/debian trixie/main amd64 libgdal-dev amd64 3.10.3+dfsg-1 [379 kB]
Get:336 http://deb.debian.org/debian trixie/main amd64 libgpg-error-l10n all 1.51-4 [114 kB]
Get:337 http://deb.debian.org/debian trixie/main amd64 libheif-plugin-aomenc amd64 1.19.8-1 [23.8 kB]
Get:338 http://deb.debian.org/debian trixie/main amd64 libnuma1 amd64 2.0.19-1 [22.2 kB]
Get:339 http://deb.debian.org/debian trixie/main amd64 libx265-215 amd64 4.1-2 [1237 kB]
Get:340 http://deb.debian.org/debian trixie/main amd64 libheif-plugin-x265 amd64 1.19.8-1 [22.0 kB]
Get:341 http://deb.debian.org/debian trixie/main amd64 libldap-common all 2.6.10+dfsg-1 [35.1 kB]
Get:342 http://deb.debian.org/debian trixie/main amd64 libpng-tools amd64 1.6.48-1 [130 kB]
Get:343 http://deb.debian.org/debian trixie/main amd64 libsasl2-modules amd64 2.1.28+dfsg1-9 [66.7 kB]
Get:344 http://deb.debian.org/debian trixie/main amd64 libspatialindex8 amd64 2.1.0-1 [276 kB]
Get:345 http://deb.debian.org/debian trixie/main amd64 libspatialindex-c8 amd64 2.1.0-1 [74.5 kB]
Get:346 http://deb.debian.org/debian trixie/main amd64 libspatialindex-dev amd64 2.1.0-1 [33.5 kB]
Get:347 http://deb.debian.org/debian trixie/main amd64 libtasn1-doc all 4.20.0-2 [325 kB]
Get:348 http://deb.debian.org/debian trixie/main amd64 libtool all 2.5.4-4 [539 kB]
Get:349 http://deb.debian.org/debian trixie/main amd64 libxau6 amd64 1:1.0.11-1 [20.4 kB]
Get:350 http://deb.debian.org/debian trixie/main amd64 libxdmcp6 amd64 1:1.1.5-1 [27.8 kB]
Get:351 http://deb.debian.org/debian trixie/main amd64 libxcb1 amd64 1.17.0-2+b1 [144 kB]
Get:352 http://deb.debian.org/debian trixie/main amd64 libx11-data all 2:1.8.12-1 [343 kB]
Get:353 http://deb.debian.org/debian trixie/main amd64 libx11-6 amd64 2:1.8.12-1 [815 kB]
Get:354 http://deb.debian.org/debian trixie/main amd64 libxext6 amd64 2:1.3.4-1+b3 [50.4 kB]
Get:355 http://deb.debian.org/debian trixie/main amd64 libxmuu1 amd64 2:1.1.3-3+b4 [21.9 kB]
Get:356 http://deb.debian.org/debian trixie/main amd64 manpages-dev all 6.9.1-1 [2122 kB]
Get:357 http://deb.debian.org/debian trixie/main amd64 proj-bin amd64 9.6.0-1 [230 kB]
Get:358 http://deb.debian.org/debian trixie/main amd64 publicsuffix all 20250328.1952-0.1 [296 kB]
Get:359 http://deb.debian.org/debian trixie/main amd64 xauth amd64 1:1.1.2-1.1 [35.9 kB]
Get:360 http://deb.debian.org/debian trixie/main amd64 gnupg-utils amd64 2.4.7-21+b3 [194 kB]
debconf: unable to initialize frontend: Dialog
debconf: (TERM is not set, so the dialog frontend is not usable.)
debconf: falling back to frontend: Readline
debconf: unable to initialize frontend: Readline
debconf: (Can't locate Term/ReadLine.pm in @INC (you may need to install the Term::ReadLine module) (@INC entries checked: /etc/perl /usr/local/lib/x86_64-linux-gnu/perl/5.40.1 /usr/local/share/perl/5.40.1 /usr/lib/x86_64-linux-gnu/perl5/5.40 /usr/share/perl5 /usr/lib/x86_64-linux-gnu/perl-base /usr/lib/x86_64-linux-gnu/perl/5.40 /usr/share/perl/5.40 /usr/local/lib/site_perl) at /usr/share/perl5/Debconf/FrontEnd/Readline.pm line 8, <STDIN> line 360.)
debconf: falling back to frontend: Teletype
debconf: unable to initialize frontend: Teletype
debconf: (This frontend requires a controlling tty.)
debconf: falling back to frontend: Noninteractive
Preconfiguring packages ...
Fetched 266 MB in 1s (282 MB/s)
Selecting previously unselected package libexpat1:amd64.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 5643 files and directories currently installed.)
Preparing to unpack .../libexpat1_2.7.1-2_amd64.deb ...
Unpacking libexpat1:amd64 (2.7.1-2) ...
Selecting previously unselected package liblocale-gettext-perl.
Preparing to unpack .../liblocale-gettext-perl_1.07-7+b1_amd64.deb ...
Unpacking liblocale-gettext-perl (1.07-7+b1) ...
Selecting previously unselected package poppler-data.
Preparing to unpack .../poppler-data_0.4.12-1_all.deb ...
Unpacking poppler-data (0.4.12-1) ...
Selecting previously unselected package libpython3.13-minimal:amd64.
Preparing to unpack .../libpython3.13-minimal_3.13.5-2_amd64.deb ...
Unpacking libpython3.13-minimal:amd64 (3.13.5-2) ...
Selecting previously unselected package python3.13-minimal.
Preparing to unpack .../python3.13-minimal_3.13.5-2_amd64.deb ...
Unpacking python3.13-minimal (3.13.5-2) ...
Setting up libpython3.13-minimal:amd64 (3.13.5-2) ...
Setting up libexpat1:amd64 (2.7.1-2) ...
Setting up python3.13-minimal (3.13.5-2) ...
Selecting previously unselected package python3-minimal.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 6526 files and directories currently installed.)
Preparing to unpack .../python3-minimal_3.13.5-1_amd64.deb ...
Unpacking python3-minimal (3.13.5-1) ...
Selecting previously unselected package media-types.
Preparing to unpack .../media-types_13.0.0_all.deb ...
Unpacking media-types (13.0.0) ...
Selecting previously unselected package libpython3.13-stdlib:amd64.
Preparing to unpack .../libpython3.13-stdlib_3.13.5-2_amd64.deb ...
Unpacking libpython3.13-stdlib:amd64 (3.13.5-2) ...
Selecting previously unselected package python3.13.
Preparing to unpack .../python3.13_3.13.5-2_amd64.deb ...
Unpacking python3.13 (3.13.5-2) ...
Selecting previously unselected package libpython3-stdlib:amd64.
Preparing to unpack .../libpython3-stdlib_3.13.5-1_amd64.deb ...
Unpacking libpython3-stdlib:amd64 (3.13.5-1) ...
Setting up python3-minimal (3.13.5-1) ...
Selecting previously unselected package python3.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 6980 files and directories currently installed.)
Preparing to unpack .../0-python3_3.13.5-1_amd64.deb ...
Unpacking python3 (3.13.5-1) ...
Selecting previously unselected package python3-numpy-dev:amd64.
Preparing to unpack .../1-python3-numpy-dev_1%3a2.2.4+ds-1_amd64.deb ...
Unpacking python3-numpy-dev:amd64 (1:2.2.4+ds-1) ...
Selecting previously unselected package libblas3:amd64.
Preparing to unpack .../2-libblas3_3.12.1-6_amd64.deb ...
Unpacking libblas3:amd64 (3.12.1-6) ...
Selecting previously unselected package libgfortran5:amd64.
Preparing to unpack .../3-libgfortran5_14.2.0-19_amd64.deb ...
Unpacking libgfortran5:amd64 (14.2.0-19) ...
Selecting previously unselected package liblapack3:amd64.
Preparing to unpack .../4-liblapack3_3.12.1-6_amd64.deb ...
Unpacking liblapack3:amd64 (3.12.1-6) ...
Selecting previously unselected package python3-numpy.
Preparing to unpack .../5-python3-numpy_1%3a2.2.4+ds-1_amd64.deb ...
Unpacking python3-numpy (1:2.2.4+ds-1) ...
Preparing to unpack .../6-libpcre2-8-0_10.46-1~deb13u1_amd64.deb ...
Unpacking libpcre2-8-0:amd64 (10.46-1~deb13u1) over (10.45-1) ...
Setting up libpcre2-8-0:amd64 (10.46-1~deb13u1) ...
Selecting previously unselected package less.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 8007 files and directories currently installed.)
Preparing to unpack .../000-less_668-1_amd64.deb ...
Unpacking less (668-1) ...
Selecting previously unselected package bash-completion.
Preparing to unpack .../001-bash-completion_1%3a2.16.0-7_all.deb ...
Unpacking bash-completion (1:2.16.0-7) ...
Selecting previously unselected package bzip2.
Preparing to unpack .../002-bzip2_1.0.8-6_amd64.deb ...
Unpacking bzip2 (1.0.8-6) ...
Selecting previously unselected package libmagic-mgc.
Preparing to unpack .../003-libmagic-mgc_1%3a5.46-5_amd64.deb ...
Unpacking libmagic-mgc (1:5.46-5) ...
Selecting previously unselected package libmagic1t64:amd64.
Preparing to unpack .../004-libmagic1t64_1%3a5.46-5_amd64.deb ...
Unpacking libmagic1t64:amd64 (1:5.46-5) ...
Selecting previously unselected package file.
Preparing to unpack .../005-file_1%3a5.46-5_amd64.deb ...
Unpacking file (1:5.46-5) ...
Selecting previously unselected package krb5-locales.
Preparing to unpack .../006-krb5-locales_1.21.3-5_all.deb ...
Unpacking krb5-locales (1.21.3-5) ...
Selecting previously unselected package manpages.
Preparing to unpack .../007-manpages_6.9.1-1_all.deb ...
Unpacking manpages (6.9.1-1) ...
Selecting previously unselected package libedit2:amd64.
Preparing to unpack .../008-libedit2_3.1-20250104-1_amd64.deb ...
Unpacking libedit2:amd64 (3.1-20250104-1) ...
Selecting previously unselected package libcbor0.10:amd64.
Preparing to unpack .../009-libcbor0.10_0.10.2-2_amd64.deb ...
Unpacking libcbor0.10:amd64 (0.10.2-2) ...
Selecting previously unselected package libfido2-1:amd64.
Preparing to unpack .../010-libfido2-1_1.15.0-1+b1_amd64.deb ...
Unpacking libfido2-1:amd64 (1.15.0-1+b1) ...
Selecting previously unselected package libkrb5support0:amd64.
Preparing to unpack .../011-libkrb5support0_1.21.3-5_amd64.deb ...
Unpacking libkrb5support0:amd64 (1.21.3-5) ...
Selecting previously unselected package libcom-err2:amd64.
Preparing to unpack .../012-libcom-err2_1.47.2-3+b3_amd64.deb ...
Unpacking libcom-err2:amd64 (1.47.2-3+b3) ...
Selecting previously unselected package libk5crypto3:amd64.
Preparing to unpack .../013-libk5crypto3_1.21.3-5_amd64.deb ...
Unpacking libk5crypto3:amd64 (1.21.3-5) ...
Selecting previously unselected package libkeyutils1:amd64.
Preparing to unpack .../014-libkeyutils1_1.6.3-6_amd64.deb ...
Unpacking libkeyutils1:amd64 (1.6.3-6) ...
Selecting previously unselected package libkrb5-3:amd64.
Preparing to unpack .../015-libkrb5-3_1.21.3-5_amd64.deb ...
Unpacking libkrb5-3:amd64 (1.21.3-5) ...
Selecting previously unselected package libgssapi-krb5-2:amd64.
Preparing to unpack .../016-libgssapi-krb5-2_1.21.3-5_amd64.deb ...
Unpacking libgssapi-krb5-2:amd64 (1.21.3-5) ...
Selecting previously unselected package openssh-client.
Preparing to unpack .../017-openssh-client_1%3a10.0p1-7_amd64.deb ...
Unpacking openssh-client (1:10.0p1-7) ...
Selecting previously unselected package perl-modules-5.40.
Preparing to unpack .../018-perl-modules-5.40_5.40.1-6_all.deb ...
Unpacking perl-modules-5.40 (5.40.1-6) ...
Selecting previously unselected package libgdbm-compat4t64:amd64.
Preparing to unpack .../019-libgdbm-compat4t64_1.24-2_amd64.deb ...
Unpacking libgdbm-compat4t64:amd64 (1.24-2) ...
Selecting previously unselected package libperl5.40:amd64.
Preparing to unpack .../020-libperl5.40_5.40.1-6_amd64.deb ...
Unpacking libperl5.40:amd64 (5.40.1-6) ...
Selecting previously unselected package perl.
Preparing to unpack .../021-perl_5.40.1-6_amd64.deb ...
Unpacking perl (5.40.1-6) ...
Selecting previously unselected package libunistring5:amd64.
Preparing to unpack .../022-libunistring5_1.3-2_amd64.deb ...
Unpacking libunistring5:amd64 (1.3-2) ...
Selecting previously unselected package libidn2-0:amd64.
Preparing to unpack .../023-libidn2-0_2.3.8-2_amd64.deb ...
Unpacking libidn2-0:amd64 (2.3.8-2) ...
Selecting previously unselected package libp11-kit0:amd64.
Preparing to unpack .../024-libp11-kit0_0.25.5-3_amd64.deb ...
Unpacking libp11-kit0:amd64 (0.25.5-3) ...
Selecting previously unselected package libtasn1-6:amd64.
Preparing to unpack .../025-libtasn1-6_4.20.0-2_amd64.deb ...
Unpacking libtasn1-6:amd64 (4.20.0-2) ...
Selecting previously unselected package libgnutls30t64:amd64.
Preparing to unpack .../026-libgnutls30t64_3.8.9-3_amd64.deb ...
Unpacking libgnutls30t64:amd64 (3.8.9-3) ...
Selecting previously unselected package libpsl5t64:amd64.
Preparing to unpack .../027-libpsl5t64_0.21.2-1.1+b1_amd64.deb ...
Unpacking libpsl5t64:amd64 (0.21.2-1.1+b1) ...
Selecting previously unselected package wget.
Preparing to unpack .../028-wget_1.25.0-2_amd64.deb ...
Unpacking wget (1.25.0-2) ...
Selecting previously unselected package xz-utils.
Preparing to unpack .../029-xz-utils_5.8.1-1_amd64.deb ...
Unpacking xz-utils (5.8.1-1) ...
Selecting previously unselected package m4.
Preparing to unpack .../030-m4_1.4.19-8_amd64.deb ...
Unpacking m4 (1.4.19-8) ...
Selecting previously unselected package autoconf.
Preparing to unpack .../031-autoconf_2.72-3.1_all.deb ...
Unpacking autoconf (2.72-3.1) ...
Selecting previously unselected package autotools-dev.
Preparing to unpack .../032-autotools-dev_20240727.1_all.deb ...
Unpacking autotools-dev (20240727.1) ...
Selecting previously unselected package automake.
Preparing to unpack .../033-automake_1%3a1.17-4_all.deb ...
Unpacking automake (1:1.17-4) ...
Selecting previously unselected package libsframe1:amd64.
Preparing to unpack .../034-libsframe1_2.44-3_amd64.deb ...
Unpacking libsframe1:amd64 (2.44-3) ...
Selecting previously unselected package binutils-common:amd64.
Preparing to unpack .../035-binutils-common_2.44-3_amd64.deb ...
Unpacking binutils-common:amd64 (2.44-3) ...
Selecting previously unselected package libbinutils:amd64.
Preparing to unpack .../036-libbinutils_2.44-3_amd64.deb ...
Unpacking libbinutils:amd64 (2.44-3) ...
Selecting previously unselected package libgprofng0:amd64.
Preparing to unpack .../037-libgprofng0_2.44-3_amd64.deb ...
Unpacking libgprofng0:amd64 (2.44-3) ...
Selecting previously unselected package libctf-nobfd0:amd64.
Preparing to unpack .../038-libctf-nobfd0_2.44-3_amd64.deb ...
Unpacking libctf-nobfd0:amd64 (2.44-3) ...
Selecting previously unselected package libctf0:amd64.
Preparing to unpack .../039-libctf0_2.44-3_amd64.deb ...
Unpacking libctf0:amd64 (2.44-3) ...
Selecting previously unselected package libjansson4:amd64.
Preparing to unpack .../040-libjansson4_2.14-2+b3_amd64.deb ...
Unpacking libjansson4:amd64 (2.14-2+b3) ...
Selecting previously unselected package binutils-x86-64-linux-gnu.
Preparing to unpack .../041-binutils-x86-64-linux-gnu_2.44-3_amd64.deb ...
Unpacking binutils-x86-64-linux-gnu (2.44-3) ...
Selecting previously unselected package binutils.
Preparing to unpack .../042-binutils_2.44-3_amd64.deb ...
Unpacking binutils (2.44-3) ...
Selecting previously unselected package libc-dev-bin.
Preparing to unpack .../043-libc-dev-bin_2.41-12_amd64.deb ...
Unpacking libc-dev-bin (2.41-12) ...
Selecting previously unselected package linux-libc-dev.
Preparing to unpack .../044-linux-libc-dev_6.12.43-1_all.deb ...
Unpacking linux-libc-dev (6.12.43-1) ...
Selecting previously unselected package libcrypt-dev:amd64.
Preparing to unpack .../045-libcrypt-dev_1%3a4.4.38-1_amd64.deb ...
Unpacking libcrypt-dev:amd64 (1:4.4.38-1) ...
Selecting previously unselected package rpcsvc-proto.
Preparing to unpack .../046-rpcsvc-proto_1.4.3-1_amd64.deb ...
Unpacking rpcsvc-proto (1.4.3-1) ...
Selecting previously unselected package libc6-dev:amd64.
Preparing to unpack .../047-libc6-dev_2.41-12_amd64.deb ...
Unpacking libc6-dev:amd64 (2.41-12) ...
Selecting previously unselected package libisl23:amd64.
Preparing to unpack .../048-libisl23_0.27-1_amd64.deb ...
Unpacking libisl23:amd64 (0.27-1) ...
Selecting previously unselected package libmpfr6:amd64.
Preparing to unpack .../049-libmpfr6_4.2.2-1_amd64.deb ...
Unpacking libmpfr6:amd64 (4.2.2-1) ...
Selecting previously unselected package libmpc3:amd64.
Preparing to unpack .../050-libmpc3_1.3.1-1+b3_amd64.deb ...
Unpacking libmpc3:amd64 (1.3.1-1+b3) ...
Selecting previously unselected package cpp-14-x86-64-linux-gnu.
Preparing to unpack .../051-cpp-14-x86-64-linux-gnu_14.2.0-19_amd64.deb ...
Unpacking cpp-14-x86-64-linux-gnu (14.2.0-19) ...
Selecting previously unselected package cpp-14.
Preparing to unpack .../052-cpp-14_14.2.0-19_amd64.deb ...
Unpacking cpp-14 (14.2.0-19) ...
Selecting previously unselected package cpp-x86-64-linux-gnu.
Preparing to unpack .../053-cpp-x86-64-linux-gnu_4%3a14.2.0-1_amd64.deb ...
Unpacking cpp-x86-64-linux-gnu (4:14.2.0-1) ...
Selecting previously unselected package cpp.
Preparing to unpack .../054-cpp_4%3a14.2.0-1_amd64.deb ...
Unpacking cpp (4:14.2.0-1) ...
Selecting previously unselected package libcc1-0:amd64.
Preparing to unpack .../055-libcc1-0_14.2.0-19_amd64.deb ...
Unpacking libcc1-0:amd64 (14.2.0-19) ...
Selecting previously unselected package libgomp1:amd64.
Preparing to unpack .../056-libgomp1_14.2.0-19_amd64.deb ...
Unpacking libgomp1:amd64 (14.2.0-19) ...
Selecting previously unselected package libitm1:amd64.
Preparing to unpack .../057-libitm1_14.2.0-19_amd64.deb ...
Unpacking libitm1:amd64 (14.2.0-19) ...
Selecting previously unselected package libatomic1:amd64.
Preparing to unpack .../058-libatomic1_14.2.0-19_amd64.deb ...
Unpacking libatomic1:amd64 (14.2.0-19) ...
Selecting previously unselected package libasan8:amd64.
Preparing to unpack .../059-libasan8_14.2.0-19_amd64.deb ...
Unpacking libasan8:amd64 (14.2.0-19) ...
Selecting previously unselected package liblsan0:amd64.
Preparing to unpack .../060-liblsan0_14.2.0-19_amd64.deb ...
Unpacking liblsan0:amd64 (14.2.0-19) ...
Selecting previously unselected package libtsan2:amd64.
Preparing to unpack .../061-libtsan2_14.2.0-19_amd64.deb ...
Unpacking libtsan2:amd64 (14.2.0-19) ...
Selecting previously unselected package libubsan1:amd64.
Preparing to unpack .../062-libubsan1_14.2.0-19_amd64.deb ...
Unpacking libubsan1:amd64 (14.2.0-19) ...
Selecting previously unselected package libhwasan0:amd64.
Preparing to unpack .../063-libhwasan0_14.2.0-19_amd64.deb ...
Unpacking libhwasan0:amd64 (14.2.0-19) ...
Selecting previously unselected package libquadmath0:amd64.
Preparing to unpack .../064-libquadmath0_14.2.0-19_amd64.deb ...
Unpacking libquadmath0:amd64 (14.2.0-19) ...
Selecting previously unselected package libgcc-14-dev:amd64.
Preparing to unpack .../065-libgcc-14-dev_14.2.0-19_amd64.deb ...
Unpacking libgcc-14-dev:amd64 (14.2.0-19) ...
Selecting previously unselected package gcc-14-x86-64-linux-gnu.
Preparing to unpack .../066-gcc-14-x86-64-linux-gnu_14.2.0-19_amd64.deb ...
Unpacking gcc-14-x86-64-linux-gnu (14.2.0-19) ...
Selecting previously unselected package gcc-14.
Preparing to unpack .../067-gcc-14_14.2.0-19_amd64.deb ...
Unpacking gcc-14 (14.2.0-19) ...
Selecting previously unselected package gcc-x86-64-linux-gnu.
Preparing to unpack .../068-gcc-x86-64-linux-gnu_4%3a14.2.0-1_amd64.deb ...
Unpacking gcc-x86-64-linux-gnu (4:14.2.0-1) ...
Selecting previously unselected package gcc.
Preparing to unpack .../069-gcc_4%3a14.2.0-1_amd64.deb ...
Unpacking gcc (4:14.2.0-1) ...
Selecting previously unselected package libstdc++-14-dev:amd64.
Preparing to unpack .../070-libstdc++-14-dev_14.2.0-19_amd64.deb ...
Unpacking libstdc++-14-dev:amd64 (14.2.0-19) ...
Selecting previously unselected package g++-14-x86-64-linux-gnu.
Preparing to unpack .../071-g++-14-x86-64-linux-gnu_14.2.0-19_amd64.deb ...
Unpacking g++-14-x86-64-linux-gnu (14.2.0-19) ...
Selecting previously unselected package g++-14.
Preparing to unpack .../072-g++-14_14.2.0-19_amd64.deb ...
Unpacking g++-14 (14.2.0-19) ...
Selecting previously unselected package g++-x86-64-linux-gnu.
Preparing to unpack .../073-g++-x86-64-linux-gnu_4%3a14.2.0-1_amd64.deb ...
Unpacking g++-x86-64-linux-gnu (4:14.2.0-1) ...
Selecting previously unselected package g++.
Preparing to unpack .../074-g++_4%3a14.2.0-1_amd64.deb ...
Unpacking g++ (4:14.2.0-1) ...
Selecting previously unselected package make.
Preparing to unpack .../075-make_4.4.1-2_amd64.deb ...
Unpacking make (4.4.1-2) ...
Selecting previously unselected package libdpkg-perl.
Preparing to unpack .../076-libdpkg-perl_1.22.21_all.deb ...
Unpacking libdpkg-perl (1.22.21) ...
Selecting previously unselected package patch.
Preparing to unpack .../077-patch_2.8-2_amd64.deb ...
Unpacking patch (2.8-2) ...
Selecting previously unselected package dpkg-dev.
Preparing to unpack .../078-dpkg-dev_1.22.21_all.deb ...
Unpacking dpkg-dev (1.22.21) ...
Selecting previously unselected package build-essential.
Preparing to unpack .../079-build-essential_12.12_amd64.deb ...
Unpacking build-essential (12.12) ...
Selecting previously unselected package cfortran.
Preparing to unpack .../080-cfortran_20210827-1.1_all.deb ...
Unpacking cfortran (20210827-1.1) ...
Selecting previously unselected package comerr-dev:amd64.
Preparing to unpack .../081-comerr-dev_2.1-1.47.2-3+b3_amd64.deb ...
Unpacking comerr-dev:amd64 (2.1-1.47.2-3+b3) ...
Selecting previously unselected package libbrotli1:amd64.
Preparing to unpack .../082-libbrotli1_1.1.0-2+b7_amd64.deb ...
Unpacking libbrotli1:amd64 (1.1.0-2+b7) ...
Selecting previously unselected package libsasl2-modules-db:amd64.
Preparing to unpack .../083-libsasl2-modules-db_2.1.28+dfsg1-9_amd64.deb ...
Unpacking libsasl2-modules-db:amd64 (2.1.28+dfsg1-9) ...
Selecting previously unselected package libsasl2-2:amd64.
Preparing to unpack .../084-libsasl2-2_2.1.28+dfsg1-9_amd64.deb ...
Unpacking libsasl2-2:amd64 (2.1.28+dfsg1-9) ...
Selecting previously unselected package libldap2:amd64.
Preparing to unpack .../085-libldap2_2.6.10+dfsg-1_amd64.deb ...
Unpacking libldap2:amd64 (2.6.10+dfsg-1) ...
Selecting previously unselected package libnghttp2-14:amd64.
Preparing to unpack .../086-libnghttp2-14_1.64.0-1.1_amd64.deb ...
Unpacking libnghttp2-14:amd64 (1.64.0-1.1) ...
Selecting previously unselected package libnghttp3-9:amd64.
Preparing to unpack .../087-libnghttp3-9_1.8.0-1_amd64.deb ...
Unpacking libnghttp3-9:amd64 (1.8.0-1) ...
Selecting previously unselected package librtmp1:amd64.
Preparing to unpack .../088-librtmp1_2.4+20151223.gitfa8646d.1-2+b5_amd64.deb ...
Unpacking librtmp1:amd64 (2.4+20151223.gitfa8646d.1-2+b5) ...
Selecting previously unselected package libssh2-1t64:amd64.
Preparing to unpack .../089-libssh2-1t64_1.11.1-1_amd64.deb ...
Unpacking libssh2-1t64:amd64 (1.11.1-1) ...
Selecting previously unselected package libcurl4t64:amd64.
Preparing to unpack .../090-libcurl4t64_8.14.1-2_amd64.deb ...
Unpacking libcurl4t64:amd64 (8.14.1-2) ...
Selecting previously unselected package curl.
Preparing to unpack .../091-curl_8.14.1-2_amd64.deb ...
Unpacking curl (8.14.1-2) ...
Selecting previously unselected package mysql-common.
Preparing to unpack .../092-mysql-common_5.8+1.1.1_all.deb ...
Unpacking mysql-common (5.8+1.1.1) ...
Selecting previously unselected package mariadb-common.
Preparing to unpack .../093-mariadb-common_1%3a11.8.3-0+deb13u1_all.deb ...
Unpacking mariadb-common (1:11.8.3-0+deb13u1) ...
Selecting previously unselected package libmariadb3:amd64.
Preparing to unpack .../094-libmariadb3_1%3a11.8.3-0+deb13u1_amd64.deb ...
Unpacking libmariadb3:amd64 (1:11.8.3-0+deb13u1) ...
Selecting previously unselected package libssl-dev:amd64.
Preparing to unpack .../095-libssl-dev_3.5.1-1_amd64.deb ...
Unpacking libssl-dev:amd64 (3.5.1-1) ...
Selecting previously unselected package zlib1g-dev:amd64.
Preparing to unpack .../096-zlib1g-dev_1%3a1.3.dfsg+really1.3.1-1+b1_amd64.deb ...
Unpacking zlib1g-dev:amd64 (1:1.3.dfsg+really1.3.1-1+b1) ...
Selecting previously unselected package libmariadb-dev.
Preparing to unpack .../097-libmariadb-dev_1%3a11.8.3-0+deb13u1_amd64.deb ...
Unpacking libmariadb-dev (1:11.8.3-0+deb13u1) ...
Selecting previously unselected package libmariadb-dev-compat.
Preparing to unpack .../098-libmariadb-dev-compat_1%3a11.8.3-0+deb13u1_amd64.deb ...
Unpacking libmariadb-dev-compat (1:11.8.3-0+deb13u1) ...
Selecting previously unselected package default-libmysqlclient-dev:amd64.
Preparing to unpack .../099-default-libmysqlclient-dev_1.1.1_amd64.deb ...
Unpacking default-libmysqlclient-dev:amd64 (1.1.1) ...
Selecting previously unselected package libgpg-error0:amd64.
Preparing to unpack .../100-libgpg-error0_1.51-4_amd64.deb ...
Unpacking libgpg-error0:amd64 (1.51-4) ...
Selecting previously unselected package libassuan9:amd64.
Preparing to unpack .../101-libassuan9_3.0.2-2_amd64.deb ...
Unpacking libassuan9:amd64 (3.0.2-2) ...
Selecting previously unselected package libgcrypt20:amd64.
Preparing to unpack .../102-libgcrypt20_1.11.0-7_amd64.deb ...
Unpacking libgcrypt20:amd64 (1.11.0-7) ...
Selecting previously unselected package gpgconf.
Preparing to unpack .../103-gpgconf_2.4.7-21+b3_amd64.deb ...
Unpacking gpgconf (2.4.7-21+b3) ...
Selecting previously unselected package libksba8:amd64.
Preparing to unpack .../104-libksba8_1.6.7-2+b1_amd64.deb ...
Unpacking libksba8:amd64 (1.6.7-2+b1) ...
Selecting previously unselected package libnpth0t64:amd64.
Preparing to unpack .../105-libnpth0t64_1.8-3_amd64.deb ...
Unpacking libnpth0t64:amd64 (1.8-3) ...
Selecting previously unselected package dirmngr.
Preparing to unpack .../106-dirmngr_2.4.7-21+b3_amd64.deb ...
Unpacking dirmngr (2.4.7-21+b3) ...
Selecting previously unselected package libfakeroot:amd64.
Preparing to unpack .../107-libfakeroot_********-1_amd64.deb ...
Unpacking libfakeroot:amd64 (********-1) ...
Selecting previously unselected package fakeroot.
Preparing to unpack .../108-fakeroot_********-1_amd64.deb ...
Unpacking fakeroot (********-1) ...
Selecting previously unselected package fonts-dejavu-mono.
Preparing to unpack .../109-fonts-dejavu-mono_2.37-8_all.deb ...
Unpacking fonts-dejavu-mono (2.37-8) ...
Selecting previously unselected package fonts-dejavu-core.
Preparing to unpack .../110-fonts-dejavu-core_2.37-8_all.deb ...
Unpacking fonts-dejavu-core (2.37-8) ...
Selecting previously unselected package fontconfig-config.
Preparing to unpack .../111-fontconfig-config_2.15.0-2.3_amd64.deb ...
Unpacking fontconfig-config (2.15.0-2.3) ...
Selecting previously unselected package gdal-data.
Preparing to unpack .../112-gdal-data_3.10.3+dfsg-1_all.deb ...
Unpacking gdal-data (3.10.3+dfsg-1) ...
Selecting previously unselected package gdal-plugins:amd64.
Preparing to unpack .../113-gdal-plugins_3.10.3+dfsg-1_amd64.deb ...
Unpacking gdal-plugins:amd64 (3.10.3+dfsg-1) ...
Selecting previously unselected package libaec0:amd64.
Preparing to unpack .../114-libaec0_1.1.3-1+b1_amd64.deb ...
Unpacking libaec0:amd64 (1.1.3-1+b1) ...
Selecting previously unselected package libarpack2t64:amd64.
Preparing to unpack .../115-libarpack2t64_3.9.1-6_amd64.deb ...
Unpacking libarpack2t64:amd64 (3.9.1-6) ...
Selecting previously unselected package libarmadillo14.
Preparing to unpack .../116-libarmadillo14_1%3a14.2.3+dfsg-1+b1_amd64.deb ...
Unpacking libarmadillo14 (1:14.2.3+dfsg-1+b1) ...
Selecting previously unselected package libaom3:amd64.
Preparing to unpack .../117-libaom3_3.12.1-1_amd64.deb ...
Unpacking libaom3:amd64 (3.12.1-1) ...
Selecting previously unselected package libdav1d7:amd64.
Preparing to unpack .../118-libdav1d7_1.5.1-1_amd64.deb ...
Unpacking libdav1d7:amd64 (1.5.1-1) ...
Selecting previously unselected package libabsl20240722:amd64.
Preparing to unpack .../119-libabsl20240722_20240722.0-4_amd64.deb ...
Unpacking libabsl20240722:amd64 (20240722.0-4) ...
Selecting previously unselected package libgav1-1:amd64.
Preparing to unpack .../120-libgav1-1_0.19.0-3+b1_amd64.deb ...
Unpacking libgav1-1:amd64 (0.19.0-3+b1) ...
Selecting previously unselected package librav1e0.7:amd64.
Preparing to unpack .../121-librav1e0.7_0.7.1-9+b2_amd64.deb ...
Unpacking librav1e0.7:amd64 (0.7.1-9+b2) ...
Selecting previously unselected package libsvtav1enc2:amd64.
Preparing to unpack .../122-libsvtav1enc2_2.3.0+dfsg-1_amd64.deb ...
Unpacking libsvtav1enc2:amd64 (2.3.0+dfsg-1) ...
Selecting previously unselected package libjpeg62-turbo:amd64.
Preparing to unpack .../123-libjpeg62-turbo_1%3a2.1.5-4_amd64.deb ...
Unpacking libjpeg62-turbo:amd64 (1:2.1.5-4) ...
Selecting previously unselected package libyuv0:amd64.
Preparing to unpack .../124-libyuv0_0.0.1904.20250204-1_amd64.deb ...
Unpacking libyuv0:amd64 (0.0.1904.20250204-1) ...
Selecting previously unselected package libavif16:amd64.
Preparing to unpack .../125-libavif16_1.2.1-1.2_amd64.deb ...
Unpacking libavif16:amd64 (1.2.1-1.2) ...
Selecting previously unselected package libsnappy1v5:amd64.
Preparing to unpack .../126-libsnappy1v5_1.2.2-1_amd64.deb ...
Unpacking libsnappy1v5:amd64 (1.2.2-1) ...
Selecting previously unselected package libblosc1:amd64.
Preparing to unpack .../127-libblosc1_1.21.5+ds-1+b2_amd64.deb ...
Unpacking libblosc1:amd64 (1.21.5+ds-1+b2) ...
Selecting previously unselected package libngtcp2-16:amd64.
Preparing to unpack .../128-libngtcp2-16_1.11.0-1_amd64.deb ...
Unpacking libngtcp2-16:amd64 (1.11.0-1) ...
Selecting previously unselected package libngtcp2-crypto-gnutls8:amd64.
Preparing to unpack .../129-libngtcp2-crypto-gnutls8_1.11.0-1_amd64.deb ...
Unpacking libngtcp2-crypto-gnutls8:amd64 (1.11.0-1) ...
Selecting previously unselected package libcurl3t64-gnutls:amd64.
Preparing to unpack .../130-libcurl3t64-gnutls_8.14.1-2_amd64.deb ...
Unpacking libcurl3t64-gnutls:amd64 (8.14.1-2) ...
Selecting previously unselected package libcfitsio10t64:amd64.
Preparing to unpack .../131-libcfitsio10t64_4.6.2-2_amd64.deb ...
Unpacking libcfitsio10t64:amd64 (4.6.2-2) ...
Selecting previously unselected package libdeflate0:amd64.
Preparing to unpack .../132-libdeflate0_1.23-2_amd64.deb ...
Unpacking libdeflate0:amd64 (1.23-2) ...
Selecting previously unselected package libminizip1t64:amd64.
Preparing to unpack .../133-libminizip1t64_1%3a1.3.dfsg+really1.3.1-1+b1_amd64.deb ...
Unpacking libminizip1t64:amd64 (1:1.3.dfsg+really1.3.1-1+b1) ...
Selecting previously unselected package libfreexl1:amd64.
Preparing to unpack .../134-libfreexl1_2.0.0-1+b3_amd64.deb ...
Unpacking libfreexl1:amd64 (2.0.0-1+b3) ...
Selecting previously unselected package libfyba0t64:amd64.
Preparing to unpack .../135-libfyba0t64_4.1.1-11+b1_amd64.deb ...
Unpacking libfyba0t64:amd64 (4.1.1-11+b1) ...
Selecting previously unselected package libgeos3.13.1:amd64.
Preparing to unpack .../136-libgeos3.13.1_3.13.1-1_amd64.deb ...
Unpacking libgeos3.13.1:amd64 (3.13.1-1) ...
Selecting previously unselected package libgeos-c1t64:amd64.
Preparing to unpack .../137-libgeos-c1t64_3.13.1-1_amd64.deb ...
Unpacking libgeos-c1t64:amd64 (3.13.1-1) ...
Selecting previously unselected package proj-data.
Preparing to unpack .../138-proj-data_9.6.0-1_all.deb ...
Unpacking proj-data (9.6.0-1) ...
Selecting previously unselected package libjbig0:amd64.
Preparing to unpack .../139-libjbig0_2.1-6.1+b2_amd64.deb ...
Unpacking libjbig0:amd64 (2.1-6.1+b2) ...
Selecting previously unselected package liblerc4:amd64.
Preparing to unpack .../140-liblerc4_4.0.0+ds-5_amd64.deb ...
Unpacking liblerc4:amd64 (4.0.0+ds-5) ...
Selecting previously unselected package libsharpyuv0:amd64.
Preparing to unpack .../141-libsharpyuv0_1.5.0-0.1_amd64.deb ...
Unpacking libsharpyuv0:amd64 (1.5.0-0.1) ...
Selecting previously unselected package libwebp7:amd64.
Preparing to unpack .../142-libwebp7_1.5.0-0.1_amd64.deb ...
Unpacking libwebp7:amd64 (1.5.0-0.1) ...
Selecting previously unselected package libtiff6:amd64.
Preparing to unpack .../143-libtiff6_4.7.0-3_amd64.deb ...
Unpacking libtiff6:amd64 (4.7.0-3) ...
Selecting previously unselected package libproj25:amd64.
Preparing to unpack .../144-libproj25_9.6.0-1_amd64.deb ...
Unpacking libproj25:amd64 (9.6.0-1) ...
Selecting previously unselected package libgeotiff5:amd64.
Preparing to unpack .../145-libgeotiff5_1.7.4-1_amd64.deb ...
Unpacking libgeotiff5:amd64 (1.7.4-1) ...
Selecting previously unselected package libgif7:amd64.
Preparing to unpack .../146-libgif7_5.2.2-1+b1_amd64.deb ...
Unpacking libgif7:amd64 (5.2.2-1+b1) ...
Selecting previously unselected package libsz2:amd64.
Preparing to unpack .../147-libsz2_1.1.3-1+b1_amd64.deb ...
Unpacking libsz2:amd64 (1.1.3-1+b1) ...
Selecting previously unselected package libhdf4-0-alt:amd64.
Preparing to unpack .../148-libhdf4-0-alt_4.3.0-1+b1_amd64.deb ...
Unpacking libhdf4-0-alt:amd64 (4.3.0-1+b1) ...
Selecting previously unselected package libhdf5-310:amd64.
Preparing to unpack .../149-libhdf5-310_1.14.5+repack-3_amd64.deb ...
Unpacking libhdf5-310:amd64 (1.14.5+repack-3) ...
Selecting previously unselected package libheif-plugin-dav1d:amd64.
Preparing to unpack .../150-libheif-plugin-dav1d_1.19.8-1_amd64.deb ...
Unpacking libheif-plugin-dav1d:amd64 (1.19.8-1) ...
Selecting previously unselected package libde265-0:amd64.
Preparing to unpack .../151-libde265-0_1.0.15-1+b3_amd64.deb ...
Unpacking libde265-0:amd64 (1.0.15-1+b3) ...
Selecting previously unselected package libheif-plugin-libde265:amd64.
Preparing to unpack .../152-libheif-plugin-libde265_1.19.8-1_amd64.deb ...
Unpacking libheif-plugin-libde265:amd64 (1.19.8-1) ...
Selecting previously unselected package libheif1:amd64.
Preparing to unpack .../153-libheif1_1.19.8-1_amd64.deb ...
Unpacking libheif1:amd64 (1.19.8-1) ...
Selecting previously unselected package libjson-c5:amd64.
Preparing to unpack .../154-libjson-c5_0.18+ds-1_amd64.deb ...
Unpacking libjson-c5:amd64 (0.18+ds-1) ...
Selecting previously unselected package liburiparser1:amd64.
Preparing to unpack .../155-liburiparser1_0.9.8+dfsg-2_amd64.deb ...
Unpacking liburiparser1:amd64 (0.9.8+dfsg-2) ...
Selecting previously unselected package libkmlbase1t64:amd64.
Preparing to unpack .../156-libkmlbase1t64_1.3.0-12+b2_amd64.deb ...
Unpacking libkmlbase1t64:amd64 (1.3.0-12+b2) ...
Selecting previously unselected package libkmldom1t64:amd64.
Preparing to unpack .../157-libkmldom1t64_1.3.0-12+b2_amd64.deb ...
Unpacking libkmldom1t64:amd64 (1.3.0-12+b2) ...
Selecting previously unselected package libkmlengine1t64:amd64.
Preparing to unpack .../158-libkmlengine1t64_1.3.0-12+b2_amd64.deb ...
Unpacking libkmlengine1t64:amd64 (1.3.0-12+b2) ...
Selecting previously unselected package libhdf5-hl-310:amd64.
Preparing to unpack .../159-libhdf5-hl-310_1.14.5+repack-3_amd64.deb ...
Unpacking libhdf5-hl-310:amd64 (1.14.5+repack-3) ...
Selecting previously unselected package libxml2:amd64.
Preparing to unpack .../160-libxml2_2.12.7+dfsg+really2.9.14-2.1+deb13u1_amd64.deb ...
Unpacking libxml2:amd64 (2.12.7+dfsg+really2.9.14-2.1+deb13u1) ...
Selecting previously unselected package libnetcdf22:amd64.
Preparing to unpack .../161-libnetcdf22_1%3a4.9.3-1_amd64.deb ...
Unpacking libnetcdf22:amd64 (1:4.9.3-1) ...
Selecting previously unselected package libltdl7:amd64.
Preparing to unpack .../162-libltdl7_2.5.4-4_amd64.deb ...
Unpacking libltdl7:amd64 (2.5.4-4) ...
Selecting previously unselected package libodbc2:amd64.
Preparing to unpack .../163-libodbc2_2.3.12-2_amd64.deb ...
Unpacking libodbc2:amd64 (2.3.12-2) ...
Selecting previously unselected package unixodbc-common.
Preparing to unpack .../164-unixodbc-common_2.3.12-2_all.deb ...
Unpacking unixodbc-common (2.3.12-2) ...
Selecting previously unselected package libodbcinst2:amd64.
Preparing to unpack .../165-libodbcinst2_2.3.12-2_amd64.deb ...
Unpacking libodbcinst2:amd64 (2.3.12-2) ...
Selecting previously unselected package libtirpc-common.
Preparing to unpack .../166-libtirpc-common_1.3.6+ds-1_all.deb ...
Unpacking libtirpc-common (1.3.6+ds-1) ...
Selecting previously unselected package libtirpc3t64:amd64.
Preparing to unpack .../167-libtirpc3t64_1.3.6+ds-1_amd64.deb ...
Adding 'diversion of /lib/x86_64-linux-gnu/libtirpc.so.3 to /lib/x86_64-linux-gnu/libtirpc.so.3.usr-is-merged by libtirpc3t64'
Adding 'diversion of /lib/x86_64-linux-gnu/libtirpc.so.3.0.0 to /lib/x86_64-linux-gnu/libtirpc.so.3.0.0.usr-is-merged by libtirpc3t64'
Unpacking libtirpc3t64:amd64 (1.3.6+ds-1) ...
Selecting previously unselected package libogdi4.1:amd64.
Preparing to unpack .../168-libogdi4.1_4.1.1+ds-5_amd64.deb ...
Unpacking libogdi4.1:amd64 (4.1.1+ds-5) ...
Selecting previously unselected package libopenjp2-7:amd64.
Preparing to unpack .../169-libopenjp2-7_2.5.3-2.1~deb13u1_amd64.deb ...
Unpacking libopenjp2-7:amd64 (2.5.3-2.1~deb13u1) ...
Selecting previously unselected package libpng16-16t64:amd64.
Preparing to unpack .../170-libpng16-16t64_1.6.48-1_amd64.deb ...
Unpacking libpng16-16t64:amd64 (1.6.48-1) ...
Selecting previously unselected package libfreetype6:amd64.
Preparing to unpack .../171-libfreetype6_2.13.3+dfsg-1_amd64.deb ...
Unpacking libfreetype6:amd64 (2.13.3+dfsg-1) ...
Selecting previously unselected package libfontconfig1:amd64.
Preparing to unpack .../172-libfontconfig1_2.15.0-2.3_amd64.deb ...
Unpacking libfontconfig1:amd64 (2.15.0-2.3) ...
Selecting previously unselected package gnupg-l10n.
Preparing to unpack .../173-gnupg-l10n_2.4.7-21_all.deb ...
Unpacking gnupg-l10n (2.4.7-21) ...
Selecting previously unselected package gpg.
Preparing to unpack .../174-gpg_2.4.7-21+b3_amd64.deb ...
Unpacking gpg (2.4.7-21+b3) ...
Selecting previously unselected package pinentry-curses.
Preparing to unpack .../175-pinentry-curses_1.3.1-2_amd64.deb ...
Unpacking pinentry-curses (1.3.1-2) ...
Selecting previously unselected package gpg-agent.
Preparing to unpack .../176-gpg-agent_2.4.7-21+b3_amd64.deb ...
Unpacking gpg-agent (2.4.7-21+b3) ...
Selecting previously unselected package gpgsm.
Preparing to unpack .../177-gpgsm_2.4.7-21+b3_amd64.deb ...
Unpacking gpgsm (2.4.7-21+b3) ...
Selecting previously unselected package gnupg.
Preparing to unpack .../178-gnupg_2.4.7-21_all.deb ...
Unpacking gnupg (2.4.7-21) ...
Selecting previously unselected package libgpgme11t64:amd64.
Preparing to unpack .../179-libgpgme11t64_1.24.2-3_amd64.deb ...
Unpacking libgpgme11t64:amd64 (1.24.2-3) ...
Selecting previously unselected package libgpgmepp6t64:amd64.
Preparing to unpack .../180-libgpgmepp6t64_1.24.2-3_amd64.deb ...
Unpacking libgpgmepp6t64:amd64 (1.24.2-3) ...
Selecting previously unselected package liblcms2-2:amd64.
Preparing to unpack .../181-liblcms2-2_2.16-2_amd64.deb ...
Unpacking liblcms2-2:amd64 (2.16-2) ...
Selecting previously unselected package libnspr4:amd64.
Preparing to unpack .../182-libnspr4_2%3a4.36-1_amd64.deb ...
Unpacking libnspr4:amd64 (2:4.36-1) ...
Selecting previously unselected package libnss3:amd64.
Preparing to unpack .../183-libnss3_2%3a3.110-1_amd64.deb ...
Unpacking libnss3:amd64 (2:3.110-1) ...
Selecting previously unselected package libpoppler147:amd64.
Preparing to unpack .../184-libpoppler147_25.03.0-5_amd64.deb ...
Unpacking libpoppler147:amd64 (25.03.0-5) ...
Selecting previously unselected package libpq5:amd64.
Preparing to unpack .../185-libpq5_17.6-0+deb13u1_amd64.deb ...
Unpacking libpq5:amd64 (17.6-0+deb13u1) ...
Selecting previously unselected package libqhull-r8.0:amd64.
Preparing to unpack .../186-libqhull-r8.0_2020.2-6+b2_amd64.deb ...
Unpacking libqhull-r8.0:amd64 (2020.2-6+b2) ...
Selecting previously unselected package librttopo1:amd64.
Preparing to unpack .../187-librttopo1_1.1.0-4_amd64.deb ...
Unpacking librttopo1:amd64 (1.1.0-4) ...
Selecting previously unselected package libspatialite8t64:amd64.
Preparing to unpack .../188-libspatialite8t64_5.1.0-3+b2_amd64.deb ...
Unpacking libspatialite8t64:amd64 (5.1.0-3+b2) ...
Selecting previously unselected package libicu76:amd64.
Preparing to unpack .../189-libicu76_76.1-4_amd64.deb ...
Unpacking libicu76:amd64 (76.1-4) ...
Selecting previously unselected package libxerces-c3.2t64:amd64.
Preparing to unpack .../190-libxerces-c3.2t64_3.2.4+debian-1.3+b2_amd64.deb ...
Unpacking libxerces-c3.2t64:amd64 (3.2.4+debian-1.3+b2) ...
Selecting previously unselected package libgdal36:amd64.
Preparing to unpack .../191-libgdal36_3.10.3+dfsg-1_amd64.deb ...
Unpacking libgdal36:amd64 (3.10.3+dfsg-1) ...
Selecting previously unselected package python3-gdal.
Preparing to unpack .../192-python3-gdal_3.10.3+dfsg-1_amd64.deb ...
Unpacking python3-gdal (3.10.3+dfsg-1) ...
Selecting previously unselected package gdal-bin.
Preparing to unpack .../193-gdal-bin_3.10.3+dfsg-1_amd64.deb ...
Unpacking gdal-bin (3.10.3+dfsg-1) ...
Selecting previously unselected package libgfortran-14-dev:amd64.
Preparing to unpack .../194-libgfortran-14-dev_14.2.0-19_amd64.deb ...
Unpacking libgfortran-14-dev:amd64 (14.2.0-19) ...
Selecting previously unselected package gfortran-14-x86-64-linux-gnu.
Preparing to unpack .../195-gfortran-14-x86-64-linux-gnu_14.2.0-19_amd64.deb ...
Unpacking gfortran-14-x86-64-linux-gnu (14.2.0-19) ...
Selecting previously unselected package gfortran-14.
Preparing to unpack .../196-gfortran-14_14.2.0-19_amd64.deb ...
Unpacking gfortran-14 (14.2.0-19) ...
Selecting previously unselected package gfortran-x86-64-linux-gnu.
Preparing to unpack .../197-gfortran-x86-64-linux-gnu_4%3a14.2.0-1_amd64.deb ...
Unpacking gfortran-x86-64-linux-gnu (4:14.2.0-1) ...
Selecting previously unselected package gfortran.
Preparing to unpack .../198-gfortran_4%3a14.2.0-1_amd64.deb ...
Unpacking gfortran (4:14.2.0-1) ...
Selecting previously unselected package liberror-perl.
Preparing to unpack .../199-liberror-perl_0.17030-1_all.deb ...
Unpacking liberror-perl (0.17030-1) ...
Selecting previously unselected package git-man.
Preparing to unpack .../200-git-man_1%3a2.47.3-0+deb13u1_all.deb ...
Unpacking git-man (1:2.47.3-0+deb13u1) ...
Selecting previously unselected package git.
Preparing to unpack .../201-git_1%3a2.47.3-0+deb13u1_amd64.deb ...
Unpacking git (1:2.47.3-0+deb13u1) ...
Selecting previously unselected package gpg-wks-client.
Preparing to unpack .../202-gpg-wks-client_2.4.7-21+b3_amd64.deb ...
Unpacking gpg-wks-client (2.4.7-21+b3) ...
Selecting previously unselected package gpgv.
Preparing to unpack .../203-gpgv_2.4.7-21+b3_amd64.deb ...
Unpacking gpgv (2.4.7-21+b3) ...
Selecting previously unselected package hdf5-helpers.
Preparing to unpack .../204-hdf5-helpers_1.14.5+repack-3_amd64.deb ...
Unpacking hdf5-helpers (1.14.5+repack-3) ...
Selecting previously unselected package icu-devtools.
Preparing to unpack .../205-icu-devtools_76.1-4_amd64.deb ...
Unpacking icu-devtools (76.1-4) ...
Selecting previously unselected package libgssrpc4t64:amd64.
Preparing to unpack .../206-libgssrpc4t64_1.21.3-5_amd64.deb ...
Unpacking libgssrpc4t64:amd64 (1.21.3-5) ...
Selecting previously unselected package libkadm5clnt-mit12:amd64.
Preparing to unpack .../207-libkadm5clnt-mit12_1.21.3-5_amd64.deb ...
Unpacking libkadm5clnt-mit12:amd64 (1.21.3-5) ...
Selecting previously unselected package libkdb5-10t64:amd64.
Preparing to unpack .../208-libkdb5-10t64_1.21.3-5_amd64.deb ...
Unpacking libkdb5-10t64:amd64 (1.21.3-5) ...
Selecting previously unselected package libkadm5srv-mit12:amd64.
Preparing to unpack .../209-libkadm5srv-mit12_1.21.3-5_amd64.deb ...
Unpacking libkadm5srv-mit12:amd64 (1.21.3-5) ...
Selecting previously unselected package krb5-multidev:amd64.
Preparing to unpack .../210-krb5-multidev_1.21.3-5_amd64.deb ...
Unpacking krb5-multidev:amd64 (1.21.3-5) ...
Selecting previously unselected package libaec-dev:amd64.
Preparing to unpack .../211-libaec-dev_1.1.3-1+b1_amd64.deb ...
Unpacking libaec-dev:amd64 (1.1.3-1+b1) ...
Selecting previously unselected package libalgorithm-diff-perl.
Preparing to unpack .../212-libalgorithm-diff-perl_1.201-1_all.deb ...
Unpacking libalgorithm-diff-perl (1.201-1) ...
Selecting previously unselected package libalgorithm-diff-xs-perl.
Preparing to unpack .../213-libalgorithm-diff-xs-perl_0.04-9_amd64.deb ...
Unpacking libalgorithm-diff-xs-perl (0.04-9) ...
Selecting previously unselected package libalgorithm-merge-perl.
Preparing to unpack .../214-libalgorithm-merge-perl_0.08-5_all.deb ...
Unpacking libalgorithm-merge-perl (0.08-5) ...
Selecting previously unselected package libaom-dev:amd64.
Preparing to unpack .../215-libaom-dev_3.12.1-1_amd64.deb ...
Unpacking libaom-dev:amd64 (3.12.1-1) ...
Selecting previously unselected package libblas-dev:amd64.
Preparing to unpack .../216-libblas-dev_3.12.1-6_amd64.deb ...
Unpacking libblas-dev:amd64 (3.12.1-6) ...
Selecting previously unselected package liblapack-dev:amd64.
Preparing to unpack .../217-liblapack-dev_3.12.1-6_amd64.deb ...
Unpacking liblapack-dev:amd64 (3.12.1-6) ...
Selecting previously unselected package libarpack2-dev:amd64.
Preparing to unpack .../218-libarpack2-dev_3.9.1-6_amd64.deb ...
Unpacking libarpack2-dev:amd64 (3.9.1-6) ...
Selecting previously unselected package libhdf5-fortran-310:amd64.
Preparing to unpack .../219-libhdf5-fortran-310_1.14.5+repack-3_amd64.deb ...
Unpacking libhdf5-fortran-310:amd64 (1.14.5+repack-3) ...
Selecting previously unselected package libhdf5-hl-fortran-310:amd64.
Preparing to unpack .../220-libhdf5-hl-fortran-310_1.14.5+repack-3_amd64.deb ...
Unpacking libhdf5-hl-fortran-310:amd64 (1.14.5+repack-3) ...
Selecting previously unselected package libhdf5-cpp-310:amd64.
Preparing to unpack .../221-libhdf5-cpp-310_1.14.5+repack-3_amd64.deb ...
Unpacking libhdf5-cpp-310:amd64 (1.14.5+repack-3) ...
Selecting previously unselected package libhdf5-hl-cpp-310:amd64.
Preparing to unpack .../222-libhdf5-hl-cpp-310_1.14.5+repack-3_amd64.deb ...
Unpacking libhdf5-hl-cpp-310:amd64 (1.14.5+repack-3) ...
Selecting previously unselected package libjpeg62-turbo-dev:amd64.
Preparing to unpack .../223-libjpeg62-turbo-dev_1%3a2.1.5-4_amd64.deb ...
Unpacking libjpeg62-turbo-dev:amd64 (1:2.1.5-4) ...
Selecting previously unselected package libjpeg-dev:amd64.
Preparing to unpack .../224-libjpeg-dev_1%3a2.1.5-4_amd64.deb ...
Unpacking libjpeg-dev:amd64 (1:2.1.5-4) ...
Selecting previously unselected package libbrotli-dev:amd64.
Preparing to unpack .../225-libbrotli-dev_1.1.0-2+b7_amd64.deb ...
Unpacking libbrotli-dev:amd64 (1.1.0-2+b7) ...
Selecting previously unselected package libidn2-dev:amd64.
Preparing to unpack .../226-libidn2-dev_2.3.8-2_amd64.deb ...
Unpacking libidn2-dev:amd64 (2.3.8-2) ...
Selecting previously unselected package libkrb5-dev:amd64.
Preparing to unpack .../227-libkrb5-dev_1.21.3-5_amd64.deb ...
Unpacking libkrb5-dev:amd64 (1.21.3-5) ...
Selecting previously unselected package libldap-dev:amd64.
Preparing to unpack .../228-libldap-dev_2.6.10+dfsg-1_amd64.deb ...
Unpacking libldap-dev:amd64 (2.6.10+dfsg-1) ...
Selecting previously unselected package libpkgconf3:amd64.
Preparing to unpack .../229-libpkgconf3_1.8.1-4_amd64.deb ...
Unpacking libpkgconf3:amd64 (1.8.1-4) ...
Selecting previously unselected package pkgconf-bin.
Preparing to unpack .../230-pkgconf-bin_1.8.1-4_amd64.deb ...
Unpacking pkgconf-bin (1.8.1-4) ...
Selecting previously unselected package pkgconf:amd64.
Preparing to unpack .../231-pkgconf_1.8.1-4_amd64.deb ...
Unpacking pkgconf:amd64 (1.8.1-4) ...
Selecting previously unselected package libnghttp2-dev:amd64.
Preparing to unpack .../232-libnghttp2-dev_1.64.0-1.1_amd64.deb ...
Unpacking libnghttp2-dev:amd64 (1.64.0-1.1) ...
Selecting previously unselected package libnghttp3-dev:amd64.
Preparing to unpack .../233-libnghttp3-dev_1.8.0-1_amd64.deb ...
Unpacking libnghttp3-dev:amd64 (1.8.0-1) ...
Selecting previously unselected package libpsl-dev:amd64.
Preparing to unpack .../234-libpsl-dev_0.21.2-1.1+b1_amd64.deb ...
Unpacking libpsl-dev:amd64 (0.21.2-1.1+b1) ...
Selecting previously unselected package libgmpxx4ldbl:amd64.
Preparing to unpack .../235-libgmpxx4ldbl_2%3a6.3.0+dfsg-3_amd64.deb ...
Unpacking libgmpxx4ldbl:amd64 (2:6.3.0+dfsg-3) ...
Selecting previously unselected package libgmp-dev:amd64.
Preparing to unpack .../236-libgmp-dev_2%3a6.3.0+dfsg-3_amd64.deb ...
Unpacking libgmp-dev:amd64 (2:6.3.0+dfsg-3) ...
Selecting previously unselected package libevent-2.1-7t64:amd64.
Preparing to unpack .../237-libevent-2.1-7t64_2.1.12-stable-10+b1_amd64.deb ...
Unpacking libevent-2.1-7t64:amd64 (2.1.12-stable-10+b1) ...
Selecting previously unselected package libunbound8:amd64.
Preparing to unpack .../238-libunbound8_1.22.0-2_amd64.deb ...
Unpacking libunbound8:amd64 (1.22.0-2) ...
Selecting previously unselected package libgnutls-dane0t64:amd64.
Preparing to unpack .../239-libgnutls-dane0t64_3.8.9-3_amd64.deb ...
Unpacking libgnutls-dane0t64:amd64 (3.8.9-3) ...
Selecting previously unselected package libgnutls-openssl27t64:amd64.
Preparing to unpack .../240-libgnutls-openssl27t64_3.8.9-3_amd64.deb ...
Unpacking libgnutls-openssl27t64:amd64 (3.8.9-3) ...
Selecting previously unselected package libp11-kit-dev:amd64.
Preparing to unpack .../241-libp11-kit-dev_0.25.5-3_amd64.deb ...
Unpacking libp11-kit-dev:amd64 (0.25.5-3) ...
Selecting previously unselected package libtasn1-6-dev:amd64.
Preparing to unpack .../242-libtasn1-6-dev_4.20.0-2_amd64.deb ...
Unpacking libtasn1-6-dev:amd64 (4.20.0-2) ...
Selecting previously unselected package nettle-dev:amd64.
Preparing to unpack .../243-nettle-dev_3.10.1-1_amd64.deb ...
Unpacking nettle-dev:amd64 (3.10.1-1) ...
Selecting previously unselected package libgnutls28-dev:amd64.
Preparing to unpack .../244-libgnutls28-dev_3.8.9-3_amd64.deb ...
Unpacking libgnutls28-dev:amd64 (3.8.9-3) ...
Selecting previously unselected package librtmp-dev:amd64.
Preparing to unpack .../245-librtmp-dev_2.4+20151223.gitfa8646d.1-2+b5_amd64.deb ...
Unpacking librtmp-dev:amd64 (2.4+20151223.gitfa8646d.1-2+b5) ...
Selecting previously unselected package libssh2-1-dev:amd64.
Preparing to unpack .../246-libssh2-1-dev_1.11.1-1_amd64.deb ...
Unpacking libssh2-1-dev:amd64 (1.11.1-1) ...
Selecting previously unselected package libzstd-dev:amd64.
Preparing to unpack .../247-libzstd-dev_1.5.7+dfsg-1_amd64.deb ...
Unpacking libzstd-dev:amd64 (1.5.7+dfsg-1) ...
Selecting previously unselected package libcurl4-openssl-dev:amd64.
Preparing to unpack .../248-libcurl4-openssl-dev_8.14.1-2_amd64.deb ...
Unpacking libcurl4-openssl-dev:amd64 (8.14.1-2) ...
Selecting previously unselected package libhdf5-dev.
Preparing to unpack .../249-libhdf5-dev_1.14.5+repack-3_amd64.deb ...
Unpacking libhdf5-dev (1.14.5+repack-3) ...
Selecting previously unselected package libsuperlu7:amd64.
Preparing to unpack .../250-libsuperlu7_7.0.1+dfsg1-2_amd64.deb ...
Unpacking libsuperlu7:amd64 (7.0.1+dfsg1-2) ...
Selecting previously unselected package libsuperlu-dev:amd64.
Preparing to unpack .../251-libsuperlu-dev_7.0.1+dfsg1-2_amd64.deb ...
Unpacking libsuperlu-dev:amd64 (7.0.1+dfsg1-2) ...
Selecting previously unselected package libarmadillo-dev.
Preparing to unpack .../252-libarmadillo-dev_1%3a14.2.3+dfsg-1+b1_amd64.deb ...
Unpacking libarmadillo-dev (1:14.2.3+dfsg-1+b1) ...
Selecting previously unselected package libdav1d-dev:amd64.
Preparing to unpack .../253-libdav1d-dev_1.5.1-1_amd64.deb ...
Unpacking libdav1d-dev:amd64 (1.5.1-1) ...
Selecting previously unselected package librav1e-dev:amd64.
Preparing to unpack .../254-librav1e-dev_0.7.1-9+b2_amd64.deb ...
Unpacking librav1e-dev:amd64 (0.7.1-9+b2) ...
Selecting previously unselected package libsvtav1enc-dev:amd64.
Preparing to unpack .../255-libsvtav1enc-dev_2.3.0+dfsg-1_amd64.deb ...
Unpacking libsvtav1enc-dev:amd64 (2.3.0+dfsg-1) ...
Selecting previously unselected package libyuv-dev:amd64.
Preparing to unpack .../256-libyuv-dev_0.0.1904.20250204-1_amd64.deb ...
Unpacking libyuv-dev:amd64 (0.0.1904.20250204-1) ...
Selecting previously unselected package libavif-dev:amd64.
Preparing to unpack .../257-libavif-dev_1.2.1-1.2_amd64.deb ...
Unpacking libavif-dev:amd64 (1.2.1-1.2) ...
Selecting previously unselected package libblosc-dev:amd64.
Preparing to unpack .../258-libblosc-dev_1.21.5+ds-1+b2_amd64.deb ...
Unpacking libblosc-dev:amd64 (1.21.5+ds-1+b2) ...
Selecting previously unselected package libboost1.83-dev:amd64.
Preparing to unpack .../259-libboost1.83-dev_1.83.0-4.2_amd64.deb ...
Unpacking libboost1.83-dev:amd64 (1.83.0-4.2) ...
Selecting previously unselected package libboost-dev:amd64.
Preparing to unpack .../260-libboost-dev_1.83.0.2+b2_amd64.deb ...
Unpacking libboost-dev:amd64 (1.83.0.2+b2) ...
Selecting previously unselected package libcfitsio-dev:amd64.
Preparing to unpack .../261-libcfitsio-dev_4.6.2-2_amd64.deb ...
Unpacking libcfitsio-dev:amd64 (4.6.2-2) ...
Selecting previously unselected package libcfitsio-doc.
Preparing to unpack .../262-libcfitsio-doc_4.6.2-2_all.deb ...
Unpacking libcfitsio-doc (4.6.2-2) ...
Selecting previously unselected package libdeflate-dev:amd64.
Preparing to unpack .../263-libdeflate-dev_1.23-2_amd64.deb ...
Unpacking libdeflate-dev:amd64 (1.23-2) ...
Selecting previously unselected package libexpat1-dev:amd64.
Preparing to unpack .../264-libexpat1-dev_2.7.1-2_amd64.deb ...
Unpacking libexpat1-dev:amd64 (2.7.1-2) ...
Selecting previously unselected package libfile-fcntllock-perl.
Preparing to unpack .../265-libfile-fcntllock-perl_0.22-4+b4_amd64.deb ...
Unpacking libfile-fcntllock-perl (0.22-4+b4) ...
Selecting previously unselected package libminizip-dev:amd64.
Preparing to unpack .../266-libminizip-dev_1%3a1.3.dfsg+really1.3.1-1+b1_amd64.deb ...
Unpacking libminizip-dev:amd64 (1:1.3.dfsg+really1.3.1-1+b1) ...
Selecting previously unselected package libfreexl-dev:amd64.
Preparing to unpack .../267-libfreexl-dev_2.0.0-1+b3_amd64.deb ...
Unpacking libfreexl-dev:amd64 (2.0.0-1+b3) ...
Selecting previously unselected package libfyba-dev:amd64.
Preparing to unpack .../268-libfyba-dev_4.1.1-11+b1_amd64.deb ...
Unpacking libfyba-dev:amd64 (4.1.1-11+b1) ...
Selecting previously unselected package libgeos-dev.
Preparing to unpack .../269-libgeos-dev_3.13.1-1_amd64.deb ...
Unpacking libgeos-dev (3.13.1-1) ...
Selecting previously unselected package libsqlite3-dev:amd64.
Preparing to unpack .../270-libsqlite3-dev_3.46.1-7_amd64.deb ...
Unpacking libsqlite3-dev:amd64 (3.46.1-7) ...
Selecting previously unselected package libjbig-dev:amd64.
Preparing to unpack .../271-libjbig-dev_2.1-6.1+b2_amd64.deb ...
Unpacking libjbig-dev:amd64 (2.1-6.1+b2) ...
Selecting previously unselected package liblzma-dev:amd64.
Preparing to unpack .../272-liblzma-dev_5.8.1-1_amd64.deb ...
Unpacking liblzma-dev:amd64 (5.8.1-1) ...
Selecting previously unselected package libwebpdemux2:amd64.
Preparing to unpack .../273-libwebpdemux2_1.5.0-0.1_amd64.deb ...
Unpacking libwebpdemux2:amd64 (1.5.0-0.1) ...
Selecting previously unselected package libwebpmux3:amd64.
Preparing to unpack .../274-libwebpmux3_1.5.0-0.1_amd64.deb ...
Unpacking libwebpmux3:amd64 (1.5.0-0.1) ...
Selecting previously unselected package libwebpdecoder3:amd64.
Preparing to unpack .../275-libwebpdecoder3_1.5.0-0.1_amd64.deb ...
Unpacking libwebpdecoder3:amd64 (1.5.0-0.1) ...
Selecting previously unselected package libsharpyuv-dev:amd64.
Preparing to unpack .../276-libsharpyuv-dev_1.5.0-0.1_amd64.deb ...
Unpacking libsharpyuv-dev:amd64 (1.5.0-0.1) ...
Selecting previously unselected package libwebp-dev:amd64.
Preparing to unpack .../277-libwebp-dev_1.5.0-0.1_amd64.deb ...
Unpacking libwebp-dev:amd64 (1.5.0-0.1) ...
Selecting previously unselected package libtiffxx6:amd64.
Preparing to unpack .../278-libtiffxx6_4.7.0-3_amd64.deb ...
Unpacking libtiffxx6:amd64 (4.7.0-3) ...
Selecting previously unselected package liblerc-dev:amd64.
Preparing to unpack .../279-liblerc-dev_4.0.0+ds-5_amd64.deb ...
Unpacking liblerc-dev:amd64 (4.0.0+ds-5) ...
Selecting previously unselected package libtiff-dev:amd64.
Preparing to unpack .../280-libtiff-dev_4.7.0-3_amd64.deb ...
Unpacking libtiff-dev:amd64 (4.7.0-3) ...
Selecting previously unselected package libproj-dev:amd64.
Preparing to unpack .../281-libproj-dev_9.6.0-1_amd64.deb ...
Unpacking libproj-dev:amd64 (9.6.0-1) ...
Selecting previously unselected package libgeotiff-dev:amd64.
Preparing to unpack .../282-libgeotiff-dev_1.7.4-1_amd64.deb ...
Unpacking libgeotiff-dev:amd64 (1.7.4-1) ...
Selecting previously unselected package libgif-dev:amd64.
Preparing to unpack .../283-libgif-dev_5.2.2-1+b1_amd64.deb ...
Unpacking libgif-dev:amd64 (5.2.2-1+b1) ...
Selecting previously unselected package libxml2-dev:amd64.
Preparing to unpack .../284-libxml2-dev_2.12.7+dfsg+really2.9.14-2.1+deb13u1_amd64.deb ...
Unpacking libxml2-dev:amd64 (2.12.7+dfsg+really2.9.14-2.1+deb13u1) ...
Selecting previously unselected package libnetcdf-dev.
Preparing to unpack .../285-libnetcdf-dev_1%3a4.9.3-1_amd64.deb ...
Unpacking libnetcdf-dev (1:4.9.3-1) ...
Selecting previously unselected package libhdf4-alt-dev.
Preparing to unpack .../286-libhdf4-alt-dev_4.3.0-1+b1_amd64.deb ...
Unpacking libhdf4-alt-dev (4.3.0-1+b1) ...
Selecting previously unselected package libheif-dev:amd64.
Preparing to unpack .../287-libheif-dev_1.19.8-1_amd64.deb ...
Unpacking libheif-dev:amd64 (1.19.8-1) ...
Selecting previously unselected package libjson-c-dev:amd64.
Preparing to unpack .../288-libjson-c-dev_0.18+ds-1_amd64.deb ...
Unpacking libjson-c-dev:amd64 (0.18+ds-1) ...
Selecting previously unselected package libkmlconvenience1t64:amd64.
Preparing to unpack .../289-libkmlconvenience1t64_1.3.0-12+b2_amd64.deb ...
Unpacking libkmlconvenience1t64:amd64 (1.3.0-12+b2) ...
Selecting previously unselected package libkmlregionator1t64:amd64.
Preparing to unpack .../290-libkmlregionator1t64_1.3.0-12+b2_amd64.deb ...
Unpacking libkmlregionator1t64:amd64 (1.3.0-12+b2) ...
Selecting previously unselected package libkmlxsd1t64:amd64.
Preparing to unpack .../291-libkmlxsd1t64_1.3.0-12+b2_amd64.deb ...
Unpacking libkmlxsd1t64:amd64 (1.3.0-12+b2) ...
Selecting previously unselected package liburiparser-dev.
Preparing to unpack .../292-liburiparser-dev_0.9.8+dfsg-2_amd64.deb ...
Unpacking liburiparser-dev (0.9.8+dfsg-2) ...
Selecting previously unselected package libkml-dev:amd64.
Preparing to unpack .../293-libkml-dev_1.3.0-12+b2_amd64.deb ...
Unpacking libkml-dev:amd64 (1.3.0-12+b2) ...
Selecting previously unselected package libltdl-dev:amd64.
Preparing to unpack .../294-libltdl-dev_2.5.4-4_amd64.deb ...
Unpacking libltdl-dev:amd64 (2.5.4-4) ...
Selecting previously unselected package libxxhash-dev:amd64.
Preparing to unpack .../295-libxxhash-dev_0.8.3-2_amd64.deb ...
Unpacking libxxhash-dev:amd64 (0.8.3-2) ...
Selecting previously unselected package liblz4-dev:amd64.
Preparing to unpack .../296-liblz4-dev_1.10.0-4_amd64.deb ...
Unpacking liblz4-dev:amd64 (1.10.0-4) ...
Selecting previously unselected package libtirpc-dev:amd64.
Preparing to unpack .../297-libtirpc-dev_1.3.6+ds-1_amd64.deb ...
Unpacking libtirpc-dev:amd64 (1.3.6+ds-1) ...
Selecting previously unselected package libogdi-dev.
Preparing to unpack .../298-libogdi-dev_4.1.1+ds-5_amd64.deb ...
Unpacking libogdi-dev (4.1.1+ds-5) ...
Selecting previously unselected package libopenjp2-7-dev:amd64.
Preparing to unpack .../299-libopenjp2-7-dev_2.5.3-2.1~deb13u1_amd64.deb ...
Unpacking libopenjp2-7-dev:amd64 (2.5.3-2.1~deb13u1) ...
Selecting previously unselected package libpcre2-16-0:amd64.
Preparing to unpack .../300-libpcre2-16-0_10.46-1~deb13u1_amd64.deb ...
Unpacking libpcre2-16-0:amd64 (10.46-1~deb13u1) ...
Selecting previously unselected package libpcre2-32-0:amd64.
Preparing to unpack .../301-libpcre2-32-0_10.46-1~deb13u1_amd64.deb ...
Unpacking libpcre2-32-0:amd64 (10.46-1~deb13u1) ...
Selecting previously unselected package libpcre2-posix3:amd64.
Preparing to unpack .../302-libpcre2-posix3_10.46-1~deb13u1_amd64.deb ...
Unpacking libpcre2-posix3:amd64 (10.46-1~deb13u1) ...
Selecting previously unselected package libpcre2-dev:amd64.
Preparing to unpack .../303-libpcre2-dev_10.46-1~deb13u1_amd64.deb ...
Unpacking libpcre2-dev:amd64 (10.46-1~deb13u1) ...
Selecting previously unselected package libpng-dev:amd64.
Preparing to unpack .../304-libpng-dev_1.6.48-1_amd64.deb ...
Unpacking libpng-dev:amd64 (1.6.48-1) ...
Selecting previously unselected package libpoppler-dev:amd64.
Preparing to unpack .../305-libpoppler-dev_25.03.0-5_amd64.deb ...
Unpacking libpoppler-dev:amd64 (25.03.0-5) ...
Selecting previously unselected package libpoppler-private-dev:amd64.
Preparing to unpack .../306-libpoppler-private-dev_25.03.0-5_amd64.deb ...
Unpacking libpoppler-private-dev:amd64 (25.03.0-5) ...
Selecting previously unselected package libpq-dev.
Preparing to unpack .../307-libpq-dev_17.6-0+deb13u1_amd64.deb ...
Unpacking libpq-dev (17.6-0+deb13u1) ...
Selecting previously unselected package libqhull8.0:amd64.
Preparing to unpack .../308-libqhull8.0_2020.2-6+b2_amd64.deb ...
Unpacking libqhull8.0:amd64 (2020.2-6+b2) ...
Selecting previously unselected package libqhullcpp8.0:amd64.
Preparing to unpack .../309-libqhullcpp8.0_2020.2-6+b2_amd64.deb ...
Unpacking libqhullcpp8.0:amd64 (2020.2-6+b2) ...
Selecting previously unselected package libqhull-dev:amd64.
Preparing to unpack .../310-libqhull-dev_2020.2-6+b2_amd64.deb ...
Unpacking libqhull-dev:amd64 (2020.2-6+b2) ...
Selecting previously unselected package librttopo-dev:amd64.
Preparing to unpack .../311-librttopo-dev_1.1.0-4_amd64.deb ...
Unpacking librttopo-dev:amd64 (1.1.0-4) ...
Selecting previously unselected package libspatialite-dev:amd64.
Preparing to unpack .../312-libspatialite-dev_5.1.0-3+b2_amd64.deb ...
Unpacking libspatialite-dev:amd64 (5.1.0-3+b2) ...
Selecting previously unselected package libicu-dev:amd64.
Preparing to unpack .../313-libicu-dev_76.1-4_amd64.deb ...
Unpacking libicu-dev:amd64 (76.1-4) ...
Selecting previously unselected package libxerces-c-dev:amd64.
Preparing to unpack .../314-libxerces-c-dev_3.2.4+debian-1.3+b2_amd64.deb ...
Unpacking libxerces-c-dev:amd64 (3.2.4+debian-1.3+b2) ...
Selecting previously unselected package libodbccr2:amd64.
Preparing to unpack .../315-libodbccr2_2.3.12-2_amd64.deb ...
Unpacking libodbccr2:amd64 (2.3.12-2) ...
Selecting previously unselected package unixodbc-dev:amd64.
Preparing to unpack .../316-unixodbc-dev_2.3.12-2_amd64.deb ...
Unpacking unixodbc-dev:amd64 (2.3.12-2) ...
Selecting previously unselected package libgdal-dev.
Preparing to unpack .../317-libgdal-dev_3.10.3+dfsg-1_amd64.deb ...
Unpacking libgdal-dev (3.10.3+dfsg-1) ...
Selecting previously unselected package libgpg-error-l10n.
Preparing to unpack .../318-libgpg-error-l10n_1.51-4_all.deb ...
Unpacking libgpg-error-l10n (1.51-4) ...
Selecting previously unselected package libheif-plugin-aomenc:amd64.
Preparing to unpack .../319-libheif-plugin-aomenc_1.19.8-1_amd64.deb ...
Unpacking libheif-plugin-aomenc:amd64 (1.19.8-1) ...
Selecting previously unselected package libnuma1:amd64.
Preparing to unpack .../320-libnuma1_2.0.19-1_amd64.deb ...
Unpacking libnuma1:amd64 (2.0.19-1) ...
Selecting previously unselected package libx265-215:amd64.
Preparing to unpack .../321-libx265-215_4.1-2_amd64.deb ...
Unpacking libx265-215:amd64 (4.1-2) ...
Selecting previously unselected package libheif-plugin-x265:amd64.
Preparing to unpack .../322-libheif-plugin-x265_1.19.8-1_amd64.deb ...
Unpacking libheif-plugin-x265:amd64 (1.19.8-1) ...
Selecting previously unselected package libldap-common.
Preparing to unpack .../323-libldap-common_2.6.10+dfsg-1_all.deb ...
Unpacking libldap-common (2.6.10+dfsg-1) ...
Selecting previously unselected package libpng-tools.
Preparing to unpack .../324-libpng-tools_1.6.48-1_amd64.deb ...
Unpacking libpng-tools (1.6.48-1) ...
Selecting previously unselected package libsasl2-modules:amd64.
Preparing to unpack .../325-libsasl2-modules_2.1.28+dfsg1-9_amd64.deb ...
Unpacking libsasl2-modules:amd64 (2.1.28+dfsg1-9) ...
Selecting previously unselected package libspatialindex8:amd64.
Preparing to unpack .../326-libspatialindex8_2.1.0-1_amd64.deb ...
Unpacking libspatialindex8:amd64 (2.1.0-1) ...
Selecting previously unselected package libspatialindex-c8:amd64.
Preparing to unpack .../327-libspatialindex-c8_2.1.0-1_amd64.deb ...
Unpacking libspatialindex-c8:amd64 (2.1.0-1) ...
Selecting previously unselected package libspatialindex-dev:amd64.
Preparing to unpack .../328-libspatialindex-dev_2.1.0-1_amd64.deb ...
Unpacking libspatialindex-dev:amd64 (2.1.0-1) ...
Selecting previously unselected package libtasn1-doc.
Preparing to unpack .../329-libtasn1-doc_4.20.0-2_all.deb ...
Unpacking libtasn1-doc (4.20.0-2) ...
Selecting previously unselected package libtool.
Preparing to unpack .../330-libtool_2.5.4-4_all.deb ...
Unpacking libtool (2.5.4-4) ...
Selecting previously unselected package libxau6:amd64.
Preparing to unpack .../331-libxau6_1%3a1.0.11-1_amd64.deb ...
Unpacking libxau6:amd64 (1:1.0.11-1) ...
Selecting previously unselected package libxdmcp6:amd64.
Preparing to unpack .../332-libxdmcp6_1%3a1.1.5-1_amd64.deb ...
Unpacking libxdmcp6:amd64 (1:1.1.5-1) ...
Selecting previously unselected package libxcb1:amd64.
Preparing to unpack .../333-libxcb1_1.17.0-2+b1_amd64.deb ...
Unpacking libxcb1:amd64 (1.17.0-2+b1) ...
Selecting previously unselected package libx11-data.
Preparing to unpack .../334-libx11-data_2%3a1.8.12-1_all.deb ...
Unpacking libx11-data (2:1.8.12-1) ...
Selecting previously unselected package libx11-6:amd64.
Preparing to unpack .../335-libx11-6_2%3a1.8.12-1_amd64.deb ...
Unpacking libx11-6:amd64 (2:1.8.12-1) ...
Selecting previously unselected package libxext6:amd64.
Preparing to unpack .../336-libxext6_2%3a1.3.4-1+b3_amd64.deb ...
Unpacking libxext6:amd64 (2:1.3.4-1+b3) ...
Selecting previously unselected package libxmuu1:amd64.
Preparing to unpack .../337-libxmuu1_2%3a1.1.3-3+b4_amd64.deb ...
Unpacking libxmuu1:amd64 (2:1.1.3-3+b4) ...
Selecting previously unselected package manpages-dev.
Preparing to unpack .../338-manpages-dev_6.9.1-1_all.deb ...
Unpacking manpages-dev (6.9.1-1) ...
Selecting previously unselected package proj-bin.
Preparing to unpack .../339-proj-bin_9.6.0-1_amd64.deb ...
Unpacking proj-bin (9.6.0-1) ...
Selecting previously unselected package publicsuffix.
Preparing to unpack .../340-publicsuffix_20250328.1952-0.1_all.deb ...
Unpacking publicsuffix (20250328.1952-0.1) ...
Selecting previously unselected package xauth.
Preparing to unpack .../341-xauth_1%3a1.1.2-1.1_amd64.deb ...
Unpacking xauth (1:1.1.2-1.1) ...
Selecting previously unselected package gnupg-utils.
Preparing to unpack .../342-gnupg-utils_2.4.7-21+b3_amd64.deb ...
Unpacking gnupg-utils (2.4.7-21+b3) ...
Setting up media-types (13.0.0) ...
Setting up liblcms2-2:amd64 (2.16-2) ...
Setting up libsharpyuv0:amd64 (1.5.0-0.1) ...
Setting up libaom3:amd64 (3.12.1-1) ...
Setting up mysql-common (5.8+1.1.1) ...
update-alternatives: using /etc/mysql/my.cnf.fallback to provide /etc/mysql/my.cnf (my.cnf) in auto mode
Setting up libxau6:amd64 (1:1.0.11-1) ...
Setting up libxdmcp6:amd64 (1:1.1.5-1) ...
Setting up libnpth0t64:amd64 (1.8-3) ...
Setting up libkeyutils1:amd64 (1.6.3-6) ...
Setting up libxcb1:amd64 (1.17.0-2+b1) ...
Setting up libzstd-dev:amd64 (1.5.7+dfsg-1) ...
Setting up liblerc4:amd64 (4.0.0+ds-5) ...
Setting up proj-data (9.6.0-1) ...
Setting up hdf5-helpers (1.14.5+repack-3) ...
Setting up libgpg-error0:amd64 (1.51-4) ...
Setting up libgdbm-compat4t64:amd64 (1.24-2) ...
Setting up libmagic-mgc (1:5.46-5) ...
Setting up libgeos3.13.1:amd64 (3.13.1-1) ...
Setting up libqhull8.0:amd64 (2020.2-6+b2) ...
Setting up libcbor0.10:amd64 (0.10.2-2) ...
Setting up manpages (6.9.1-1) ...
Setting up libtirpc-common (1.3.6+ds-1) ...
Setting up libgeos-c1t64:amd64 (3.13.1-1) ...
Setting up libtasn1-doc (4.20.0-2) ...
Setting up libbrotli1:amd64 (1.1.0-2+b7) ...
Setting up libedit2:amd64 (3.1-20250104-1) ...
Setting up libsasl2-modules:amd64 (2.1.28+dfsg1-9) ...
Setting up libmagic1t64:amd64 (1:5.46-5) ...
Setting up binutils-common:amd64 (2.44-3) ...
Setting up libnghttp2-14:amd64 (1.64.0-1.1) ...
Setting up libdeflate0:amd64 (1.23-2) ...
Setting up less (668-1) ...
Setting up linux-libc-dev (6.12.43-1) ...
Setting up libwebpdecoder3:amd64 (1.5.0-0.1) ...
Setting up libctf-nobfd0:amd64 (2.44-3) ...
Setting up m4 (1.4.19-8) ...
Setting up libevent-2.1-7t64:amd64 (2.1.12-stable-10+b1) ...
Setting up libgcrypt20:amd64 (1.11.0-7) ...
Setting up libqhull-r8.0:amd64 (2020.2-6+b2) ...
Setting up krb5-locales (1.21.3-5) ...
Setting up libcom-err2:amd64 (1.47.2-3+b3) ...
Setting up file (1:5.46-5) ...
Setting up libgomp1:amd64 (14.2.0-19) ...
Setting up libabsl20240722:amd64 (20240722.0-4) ...
Setting up bzip2 (1.0.8-6) ...
Setting up libldap-common (2.6.10+dfsg-1) ...
Setting up libjbig0:amd64 (2.1-6.1+b2) ...
Setting up libpcre2-16-0:amd64 (10.46-1~deb13u1) ...
Setting up libaec0:amd64 (1.1.3-1+b1) ...
Setting up gdal-data (3.10.3+dfsg-1) ...
Setting up libsframe1:amd64 (2.44-3) ...
Setting up libfakeroot:amd64 (********-1) ...
Setting up libjansson4:amd64 (2.14-2+b3) ...
Setting up libsnappy1v5:amd64 (1.2.2-1) ...
Setting up poppler-data (0.4.12-1) ...
Setting up libkrb5support0:amd64 (1.21.3-5) ...
Setting up libaom-dev:amd64 (3.12.1-1) ...
Setting up libsasl2-modules-db:amd64 (2.1.28+dfsg1-9) ...
Setting up fakeroot (********-1) ...
update-alternatives: using /usr/bin/fakeroot-sysv to provide /usr/bin/fakeroot (fakeroot) in auto mode
update-alternatives: warning: skip creation of /usr/share/man/man1/fakeroot.1.gz because associated file /usr/share/man/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/faked.1.gz because associated file /usr/share/man/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/es/man1/fakeroot.1.gz because associated file /usr/share/man/es/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/es/man1/faked.1.gz because associated file /usr/share/man/es/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/fr/man1/fakeroot.1.gz because associated file /usr/share/man/fr/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/fr/man1/faked.1.gz because associated file /usr/share/man/fr/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/sv/man1/fakeroot.1.gz because associated file /usr/share/man/sv/man1/fakeroot-sysv.1.gz (of link group fakeroot) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/sv/man1/faked.1.gz because associated file /usr/share/man/sv/man1/faked-sysv.1.gz (of link group fakeroot) doesn't exist
Setting up mariadb-common (1:11.8.3-0+deb13u1) ...
update-alternatives: using /etc/mysql/mariadb.cnf to provide /etc/mysql/my.cnf (my.cnf) in auto mode
Setting up libspatialindex8:amd64 (2.1.0-1) ...
Setting up autotools-dev (20240727.1) ...
Setting up libpcre2-32-0:amd64 (10.46-1~deb13u1) ...
Setting up libblas3:amd64 (3.12.1-6) ...
update-alternatives: using /usr/lib/x86_64-linux-gnu/blas/libblas.so.3 to provide /usr/lib/x86_64-linux-gnu/libblas.so.3 (libblas.so.3-x86_64-linux-gnu) in auto mode
Setting up libunbound8:amd64 (1.22.0-2) ...
Setting up libpkgconf3:amd64 (1.8.1-4) ...
Setting up libgmpxx4ldbl:amd64 (2:6.3.0+dfsg-3) ...
Setting up rpcsvc-proto (1.4.3-1) ...
Setting up libjpeg62-turbo:amd64 (1:2.1.5-4) ...
Setting up libx11-data (2:1.8.12-1) ...
Setting up make (4.4.1-2) ...
Setting up libsvtav1enc2:amd64 (2.3.0+dfsg-1) ...
Setting up libmpfr6:amd64 (4.2.2-1) ...
Setting up libnspr4:amd64 (2:4.36-1) ...
Setting up gnupg-l10n (2.4.7-21) ...
Setting up bash-completion (1:2.16.0-7) ...
Setting up libgeos-dev (3.13.1-1) ...
Setting up xz-utils (5.8.1-1) ...
update-alternatives: using /usr/bin/xz to provide /usr/bin/lzma (lzma) in auto mode
update-alternatives: warning: skip creation of /usr/share/man/man1/lzma.1.gz because associated file /usr/share/man/man1/xz.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/unlzma.1.gz because associated file /usr/share/man/man1/unxz.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/lzcat.1.gz because associated file /usr/share/man/man1/xzcat.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/lzmore.1.gz because associated file /usr/share/man/man1/xzmore.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/lzless.1.gz because associated file /usr/share/man/man1/xzless.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/lzdiff.1.gz because associated file /usr/share/man/man1/xzdiff.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/lzcmp.1.gz because associated file /usr/share/man/man1/xzcmp.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/lzgrep.1.gz because associated file /usr/share/man/man1/xzgrep.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/lzegrep.1.gz because associated file /usr/share/man/man1/xzegrep.1.gz (of link group lzma) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/lzfgrep.1.gz because associated file /usr/share/man/man1/xzfgrep.1.gz (of link group lzma) doesn't exist
Setting up libquadmath0:amd64 (14.2.0-19) ...
Setting up libp11-kit0:amd64 (0.25.5-3) ...
Setting up libunistring5:amd64 (1.3-2) ...
Setting up fonts-dejavu-mono (2.37-8) ...
Setting up libssl-dev:amd64 (3.5.1-1) ...
Setting up libpng16-16t64:amd64 (1.6.48-1) ...
Setting up libmpc3:amd64 (1.3.1-1+b3) ...
Setting up python3-numpy-dev:amd64 (1:2.2.4+ds-1) ...
Setting up libatomic1:amd64 (14.2.0-19) ...
Setting up patch (2.8-2) ...
Setting up libmariadb3:amd64 (1:11.8.3-0+deb13u1) ...
Setting up fonts-dejavu-core (2.37-8) ...
Setting up unixodbc-common (2.3.12-2) ...
Setting up libqhullcpp8.0:amd64 (2020.2-6+b2) ...
Setting up libgav1-1:amd64 (0.19.0-3+b1) ...
Setting up pkgconf-bin (1.8.1-4) ...
Setting up liblerc-dev:amd64 (4.0.0+ds-5) ...
Setting up libk5crypto3:amd64 (1.21.3-5) ...
Setting up libltdl7:amd64 (2.5.4-4) ...
Setting up libqhull-dev:amd64 (2020.2-6+b2) ...
Setting up libsasl2-2:amd64 (2.1.28+dfsg1-9) ...
Setting up libgfortran5:amd64 (14.2.0-19) ...
Setting up libnghttp3-9:amd64 (1.8.0-1) ...
Setting up libxxhash-dev:amd64 (0.8.3-2) ...
Setting up libwebp7:amd64 (1.5.0-0.1) ...
Setting up libnghttp3-dev:amd64 (1.8.0-1) ...
Setting up libspatialindex-c8:amd64 (2.1.0-1) ...
Setting up liblzma-dev:amd64 (5.8.1-1) ...
Setting up libubsan1:amd64 (14.2.0-19) ...
Setting up libgif7:amd64 (5.2.2-1+b1) ...
Setting up libodbc2:amd64 (2.3.12-2) ...
Setting up liburiparser1:amd64 (0.9.8+dfsg-2) ...
Setting up libnuma1:amd64 (2.0.19-1) ...
Setting up libpcre2-posix3:amd64 (10.46-1~deb13u1) ...
Setting up libfyba0t64:amd64 (4.1.1-11+b1) ...
Setting up librttopo1:amd64 (1.1.0-4) ...
Setting up libdav1d7:amd64 (1.5.1-1) ...
Setting up perl-modules-5.40 (5.40.1-6) ...
Setting up libminizip1t64:amd64 (1:1.3.dfsg+really1.3.1-1+b1) ...
Setting up libgif-dev:amd64 (5.2.2-1+b1) ...
Setting up libhwasan0:amd64 (14.2.0-19) ...
Setting up gpgv (2.4.7-21+b3) ...
Setting up libcrypt-dev:amd64 (1:4.4.38-1) ...
Setting up cfortran (20210827-1.1) ...
Setting up libtiff6:amd64 (4.7.0-3) ...
Setting up librav1e0.7:amd64 (0.7.1-9+b2) ...
Setting up libasan8:amd64 (14.2.0-19) ...
Setting up libassuan9:amd64 (3.0.2-2) ...
Setting up libblosc1:amd64 (1.21.5+ds-1+b2) ...
Setting up gpgconf (2.4.7-21+b3) ...
Setting up libtasn1-6:amd64 (4.20.0-2) ...
Setting up libopenjp2-7:amd64 (2.5.3-2.1~deb13u1) ...
Setting up git-man (1:2.47.3-0+deb13u1) ...
Setting up libx11-6:amd64 (2:1.8.12-1) ...
Setting up libngtcp2-16:amd64 (1.11.0-1) ...
Setting up libkrb5-3:amd64 (1.21.3-5) ...
Setting up libicu76:amd64 (76.1-4) ...
Setting up libssh2-1t64:amd64 (1.11.1-1) ...
Setting up libtsan2:amd64 (14.2.0-19) ...
Setting up libbinutils:amd64 (2.44-3) ...
Setting up libfido2-1:amd64 (1.15.0-1+b1) ...
Setting up libisl23:amd64 (0.27-1) ...
Setting up libde265-0:amd64 (1.0.15-1+b3) ...
Setting up libsharpyuv-dev:amd64 (1.5.0-0.1) ...
Setting up libc-dev-bin (2.41-12) ...
Setting up libkmlbase1t64:amd64 (1.3.0-12+b2) ...
Setting up libx265-215:amd64 (4.1-2) ...
Setting up libtasn1-6-dev:amd64 (4.20.0-2) ...
Setting up libwebpmux3:amd64 (1.5.0-0.1) ...
Setting up libgpg-error-l10n (1.51-4) ...
Setting up libtiffxx6:amd64 (4.7.0-3) ...
Setting up libdeflate-dev:amd64 (1.23-2) ...
Setting up libyuv0:amd64 (0.0.1904.20250204-1) ...
Setting up libpython3.13-stdlib:amd64 (3.13.5-2) ...
Setting up libjson-c5:amd64 (0.18+ds-1) ...
Setting up publicsuffix (20250328.1952-0.1) ...
Setting up libxml2:amd64 (2.12.7+dfsg+really2.9.14-2.1+deb13u1) ...
Setting up libcfitsio-doc (4.6.2-2) ...
Setting up libcc1-0:amd64 (14.2.0-19) ...
Setting up libldap2:amd64 (2.6.10+dfsg-1) ...
Setting up libxmuu1:amd64 (2:1.1.3-3+b4) ...
Setting up liblocale-gettext-perl (1.07-7+b1) ...
Setting up libbrotli-dev:amd64 (1.1.0-2+b7) ...
Setting up liblsan0:amd64 (14.2.0-19) ...
Setting up libp11-kit-dev:amd64 (0.25.5-3) ...
Setting up libblas-dev:amd64 (3.12.1-6) ...
update-alternatives: using /usr/lib/x86_64-linux-gnu/blas/libblas.so to provide /usr/lib/x86_64-linux-gnu/libblas.so (libblas.so-x86_64-linux-gnu) in auto mode
Setting up libsz2:amd64 (1.1.3-1+b1) ...
Setting up libitm1:amd64 (14.2.0-19) ...
Setting up libpython3-stdlib:amd64 (3.13.5-1) ...
Setting up libodbccr2:amd64 (2.3.12-2) ...
Setting up gdal-plugins:amd64 (3.10.3+dfsg-1) ...
Setting up libctf0:amd64 (2.44-3) ...
Setting up libksba8:amd64 (1.6.7-2+b1) ...
Setting up pinentry-curses (1.3.1-2) ...
Setting up librttopo-dev:amd64 (1.1.0-4) ...
Setting up manpages-dev (6.9.1-1) ...
Setting up libodbcinst2:amd64 (2.3.12-2) ...
Setting up libavif16:amd64 (1.2.1-1.2) ...
Setting up liblapack3:amd64 (3.12.1-6) ...
update-alternatives: using /usr/lib/x86_64-linux-gnu/lapack/liblapack.so.3 to provide /usr/lib/x86_64-linux-gnu/liblapack.so.3 (liblapack.so.3-x86_64-linux-gnu) in auto mode
Setting up libkmlxsd1t64:amd64 (1.3.0-12+b2) ...
Setting up libarpack2t64:amd64 (3.9.1-6) ...
Setting up libgmp-dev:amd64 (2:6.3.0+dfsg-3) ...
Setting up gpg-agent (2.4.7-21+b3) ...
Setting up nettle-dev:amd64 (3.10.1-1) ...
Setting up libpng-tools (1.6.48-1) ...
Setting up python3.13 (3.13.5-2) ...
Setting up libblosc-dev:amd64 (1.21.5+ds-1+b2) ...
Setting up fontconfig-config (2.15.0-2.3) ...
debconf: unable to initialize frontend: Dialog
debconf: (TERM is not set, so the dialog frontend is not usable.)
debconf: falling back to frontend: Readline
debconf: unable to initialize frontend: Readline
debconf: (This frontend requires a controlling tty.)
debconf: falling back to frontend: Teletype
debconf: unable to initialize frontend: Teletype
debconf: (This frontend requires a controlling tty.)
debconf: falling back to frontend: Noninteractive
Setting up libjson-c-dev:amd64 (0.18+ds-1) ...
Setting up libfyba-dev:amd64 (4.1.1-11+b1) ...
Setting up libwebpdemux2:amd64 (1.5.0-0.1) ...
Setting up gpgsm (2.4.7-21+b3) ...
Setting up libaec-dev:amd64 (1.1.3-1+b1) ...
Setting up libjbig-dev:amd64 (2.1-6.1+b2) ...
Setting up libxext6:amd64 (2:1.3.4-1+b3) ...
Setting up python3 (3.13.5-1) ...
running python rtupdate hooks for python3.13...
running python post-rtupdate hooks for python3.13...
Setting up libidn2-0:amd64 (2.3.8-2) ...
Setting up libnss3:amd64 (2:3.110-1) ...
Setting up libspatialindex-dev:amd64 (2.1.0-1) ...
Setting up libsvtav1enc-dev:amd64 (2.3.0+dfsg-1) ...
Setting up libsuperlu7:amd64 (7.0.1+dfsg1-2) ...
Setting up pkgconf:amd64 (1.8.1-4) ...
Setting up liburiparser-dev (0.9.8+dfsg-2) ...
Setting up libkmldom1t64:amd64 (1.3.0-12+b2) ...
Setting up libminizip-dev:amd64 (1:1.3.dfsg+really1.3.1-1+b1) ...
Setting up libperl5.40:amd64 (5.40.1-6) ...
Setting up perl (5.40.1-6) ...
Setting up libxml2-dev:amd64 (2.12.7+dfsg+really2.9.14-2.1+deb13u1) ...
Setting up libgprofng0:amd64 (2.44-3) ...
Setting up libwebp-dev:amd64 (1.5.0-0.1) ...
Setting up libfreetype6:amd64 (2.13.3+dfsg-1) ...
Setting up libopenjp2-7-dev:amd64 (2.5.3-2.1~deb13u1) ...
Setting up libldap-dev:amd64 (2.6.10+dfsg-1) ...
Setting up libdav1d-dev:amd64 (1.5.1-1) ...
Setting up liblapack-dev:amd64 (3.12.1-6) ...
update-alternatives: using /usr/lib/x86_64-linux-gnu/lapack/liblapack.so to provide /usr/lib/x86_64-linux-gnu/liblapack.so (liblapack.so-x86_64-linux-gnu) in auto mode
Setting up libgssapi-krb5-2:amd64 (1.21.3-5) ...
Setting up icu-devtools (76.1-4) ...
Setting up liblz4-dev:amd64 (1.10.0-4) ...
Setting up cpp-14-x86-64-linux-gnu (14.2.0-19) ...
Setting up python3-numpy (1:2.2.4+ds-1) ...
Setting up libidn2-dev:amd64 (2.3.8-2) ...
Setting up libdpkg-perl (1.22.21) ...
Setting up cpp-14 (14.2.0-19) ...
Setting up autoconf (2.72-3.1) ...
Setting up libhdf4-0-alt:amd64 (4.3.0-1+b1) ...
Setting up librav1e-dev:amd64 (0.7.1-9+b2) ...
Setting up libyuv-dev:amd64 (0.0.1904.20250204-1) ...
Setting up libfreexl1:amd64 (2.0.0-1+b3) ...
Setting up xauth (1:1.1.2-1.1) ...
Setting up libc6-dev:amd64 (2.41-12) ...
Setting up libarmadillo14 (1:14.2.3+dfsg-1+b1) ...
Setting up libfontconfig1:amd64 (2.15.0-2.3) ...
Setting up libgcc-14-dev:amd64 (14.2.0-19) ...
Setting up libsuperlu-dev:amd64 (7.0.1+dfsg1-2) ...
Setting up libstdc++-14-dev:amd64 (14.2.0-19) ...
Setting up libicu-dev:amd64 (76.1-4) ...
Setting up unixodbc-dev:amd64 (2.3.12-2) ...
Setting up gpg (2.4.7-21+b3) ...
Setting up gnupg-utils (2.4.7-21+b3) ...
Setting up binutils-x86-64-linux-gnu (2.44-3) ...
Setting up cpp-x86-64-linux-gnu (4:14.2.0-1) ...
Setting up libgnutls30t64:amd64 (3.8.9-3) ...
Setting up automake (1:1.17-4) ...
update-alternatives: using /usr/bin/automake-1.17 to provide /usr/bin/automake (automake) in auto mode
update-alternatives: warning: skip creation of /usr/share/man/man1/automake.1.gz because associated file /usr/share/man/man1/automake-1.17.1.gz (of link group automake) doesn't exist
update-alternatives: warning: skip creation of /usr/share/man/man1/aclocal.1.gz because associated file /usr/share/man/man1/aclocal-1.17.1.gz (of link group automake) doesn't exist
Setting up libgfortran-14-dev:amd64 (14.2.0-19) ...
Setting up libgnutls-openssl27t64:amd64 (3.8.9-3) ...
Setting up libnghttp2-dev:amd64 (1.64.0-1.1) ...
Setting up libboost1.83-dev:amd64 (1.83.0-4.2) ...
Setting up libarpack2-dev:amd64 (3.9.1-6) ...
Setting up libpcre2-dev:amd64 (10.46-1~deb13u1) ...
Setting up libtirpc3t64:amd64 (1.3.6+ds-1) ...
Setting up libogdi4.1:amd64 (4.1.1+ds-5) ...
Setting up libfile-fcntllock-perl (0.22-4+b4) ...
Setting up libalgorithm-diff-perl (1.201-1) ...
Setting up openssh-client (1:10.0p1-7) ...
Setting up libpsl5t64:amd64 (0.21.2-1.1+b1) ...
Setting up libpq5:amd64 (17.6-0+deb13u1) ...
Setting up libpq-dev (17.6-0+deb13u1) ...
Setting up binutils (2.44-3) ...
Setting up libkmlengine1t64:amd64 (1.3.0-12+b2) ...
Setting up dpkg-dev (1.22.21) ...
Setting up libpsl-dev:amd64 (0.21.2-1.1+b1) ...
Setting up liberror-perl (0.17030-1) ...
Setting up dirmngr (2.4.7-21+b3) ...
Setting up libltdl-dev:amd64 (2.5.4-4) ...
Setting up libtirpc-dev:amd64 (1.3.6+ds-1) ...
Setting up libexpat1-dev:amd64 (2.7.1-2) ...
Setting up libgnutls-dane0t64:amd64 (3.8.9-3) ...
Setting up libsqlite3-dev:amd64 (3.46.1-7) ...
Setting up libjpeg62-turbo-dev:amd64 (1:2.1.5-4) ...
Setting up librtmp1:amd64 (2.4+20151223.gitfa8646d.1-2+b5) ...
Setting up libgssrpc4t64:amd64 (1.21.3-5) ...
Setting up comerr-dev:amd64 (2.1-1.47.2-3+b3) ...
Setting up libavif-dev:amd64 (1.2.1-1.2) ...
Setting up zlib1g-dev:amd64 (1:1.3.dfsg+really1.3.1-1+b1) ...
Setting up cpp (4:14.2.0-1) ...
Setting up gnupg (2.4.7-21) ...
Setting up libboost-dev:amd64 (1.83.0.2+b2) ...
Setting up libgpgme11t64:amd64 (1.24.2-3) ...
Setting up libkmlconvenience1t64:amd64 (1.3.0-12+b2) ...
Setting up gcc-14-x86-64-linux-gnu (14.2.0-19) ...
Setting up libalgorithm-diff-xs-perl (0.04-9) ...
Setting up libogdi-dev (4.1.1+ds-5) ...
Setting up libngtcp2-crypto-gnutls8:amd64 (1.11.0-1) ...
Setting up libkadm5clnt-mit12:amd64 (1.21.3-5) ...
Setting up libalgorithm-merge-perl (0.08-5) ...
Setting up libgnutls28-dev:amd64 (3.8.9-3) ...
Setting up libgpgmepp6t64:amd64 (1.24.2-3) ...
Setting up libkmlregionator1t64:amd64 (1.3.0-12+b2) ...
Setting up wget (1.25.0-2) ...
Setting up gcc-x86-64-linux-gnu (4:14.2.0-1) ...
Setting up gpg-wks-client (2.4.7-21+b3) ...
Setting up libcurl4t64:amd64 (8.14.1-2) ...
Setting up libkdb5-10t64:amd64 (1.21.3-5) ...
Setting up libfreexl-dev:amd64 (2.0.0-1+b3) ...
Setting up libpng-dev:amd64 (1.6.48-1) ...
Setting up libjpeg-dev:amd64 (1:2.1.5-4) ...
Setting up libcurl3t64-gnutls:amd64 (8.14.1-2) ...
Setting up libxerces-c3.2t64:amd64 (3.2.4+debian-1.3+b2) ...
Setting up gcc-14 (14.2.0-19) ...
Setting up gfortran-14-x86-64-linux-gnu (14.2.0-19) ...
Setting up libcfitsio10t64:amd64 (4.6.2-2) ...
Setting up libtiff-dev:amd64 (4.7.0-3) ...
Setting up libxerces-c-dev:amd64 (3.2.4+debian-1.3+b2) ...
Setting up librtmp-dev:amd64 (2.4+20151223.gitfa8646d.1-2+b5) ...
Setting up git (1:2.47.3-0+deb13u1) ...
Setting up libssh2-1-dev:amd64 (1.11.1-1) ...
Setting up g++-14-x86-64-linux-gnu (14.2.0-19) ...
Setting up libmariadb-dev (1:11.8.3-0+deb13u1) ...
Setting up libhdf5-310:amd64 (1.14.5+repack-3) ...
Setting up libkadm5srv-mit12:amd64 (1.21.3-5) ...
Setting up g++-x86-64-linux-gnu (4:14.2.0-1) ...
Setting up curl (8.14.1-2) ...
Setting up g++-14 (14.2.0-19) ...
Setting up gfortran-14 (14.2.0-19) ...
Setting up libhdf5-fortran-310:amd64 (1.14.5+repack-3) ...
Setting up krb5-multidev:amd64 (1.21.3-5) ...
Setting up libhdf5-cpp-310:amd64 (1.14.5+repack-3) ...
Setting up libkml-dev:amd64 (1.3.0-12+b2) ...
Setting up libmariadb-dev-compat (1:11.8.3-0+deb13u1) ...
Setting up libhdf5-hl-310:amd64 (1.14.5+repack-3) ...
Setting up libcfitsio-dev:amd64 (4.6.2-2) ...
Setting up libpoppler147:amd64 (25.03.0-5) ...
Setting up libproj25:amd64 (9.6.0-1) ...
Setting up libtool (2.5.4-4) ...
Setting up libnetcdf22:amd64 (1:4.9.3-1) ...
Setting up proj-bin (9.6.0-1) ...
Setting up gfortran-x86-64-linux-gnu (4:14.2.0-1) ...
Setting up gcc (4:14.2.0-1) ...
Setting up libgeotiff5:amd64 (1.7.4-1) ...
Setting up libkrb5-dev:amd64 (1.21.3-5) ...
Setting up libcurl4-openssl-dev:amd64 (8.14.1-2) ...
Setting up libproj-dev:amd64 (9.6.0-1) ...
Setting up g++ (4:14.2.0-1) ...
update-alternatives: using /usr/bin/g++ to provide /usr/bin/c++ (c++) in auto mode
Setting up build-essential (12.12) ...
Setting up libspatialite8t64:amd64 (5.1.0-3+b2) ...
Setting up default-libmysqlclient-dev:amd64 (1.1.1) ...
Setting up libpoppler-dev:amd64 (25.03.0-5) ...
Setting up libhdf5-hl-cpp-310:amd64 (1.14.5+repack-3) ...
Setting up libhdf5-hl-fortran-310:amd64 (1.14.5+repack-3) ...
Setting up libgeotiff-dev:amd64 (1.7.4-1) ...
Setting up libpoppler-private-dev:amd64 (25.03.0-5) ...
Setting up gfortran (4:14.2.0-1) ...
update-alternatives: using /usr/bin/gfortran to provide /usr/bin/f95 (f95) in auto mode
update-alternatives: using /usr/bin/gfortran to provide /usr/bin/f77 (f77) in auto mode
Setting up libhdf5-dev (1.14.5+repack-3) ...
update-alternatives: using /usr/lib/x86_64-linux-gnu/pkgconfig/hdf5-serial.pc to provide /usr/lib/x86_64-linux-gnu/pkgconfig/hdf5.pc (hdf5.pc) in auto mode
Setting up libnetcdf-dev (1:4.9.3-1) ...
Setting up libspatialite-dev:amd64 (5.1.0-3+b2) ...
Setting up libarmadillo-dev (1:14.2.3+dfsg-1+b1) ...
Setting up libhdf4-alt-dev (4.3.0-1+b1) ...
Setting up libheif-plugin-libde265:amd64 (1.19.8-1) ...
Setting up libheif1:amd64 (1.19.8-1) ...
Setting up libheif-dev:amd64 (1.19.8-1) ...
Setting up libgdal36:amd64 (3.10.3+dfsg-1) ...
Setting up libheif-plugin-dav1d:amd64 (1.19.8-1) ...
Setting up python3-gdal (3.10.3+dfsg-1) ...
Setting up libheif-plugin-x265:amd64 (1.19.8-1) ...
Setting up libheif-plugin-aomenc:amd64 (1.19.8-1) ...
Setting up gdal-bin (3.10.3+dfsg-1) ...
Setting up libgdal-dev (3.10.3+dfsg-1) ...
Processing triggers for libc-bin (2.41-12) ...
DONE 47.6s

--> RUN useradd -m -u 1000 user
DONE 0.2s

--> WORKDIR /home/<USER>/app
DONE 0.0s

--> COPY --chown=user ./requirements.txt requirements.txt
DONE 0.0s

--> RUN pip install --no-cache-dir --upgrade pip
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pip in /usr/local/lib/python3.11/site-packages (24.0)
Collecting pip
  Downloading pip-25.2-py3-none-any.whl.metadata (4.7 kB)
Downloading pip-25.2-py3-none-any.whl (1.8 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.8/1.8 MB 49.8 MB/s eta 0:00:00
Installing collected packages: pip
Successfully installed pip-25.2

[notice] A new release of pip is available: 24.0 -> 25.2
[notice] To update, run: python3.11 -m pip install --upgrade pip
DONE 3.5s

--> RUN pip install --no-cache-dir --user     -r requirements.txt
Collecting fastapi==0.104.1 (from -r requirements.txt (line 4))
  Downloading fastapi-0.104.1-py3-none-any.whl.metadata (24 kB)
Collecting uvicorn==0.24.0 (from uvicorn[standard]==0.24.0->-r requirements.txt (line 5))
  Downloading uvicorn-0.24.0-py3-none-any.whl.metadata (6.4 kB)
Collecting pydantic==2.5.0 (from -r requirements.txt (line 6))
  Downloading pydantic-2.5.0-py3-none-any.whl.metadata (174 kB)
Collecting pydantic-settings==2.1.0 (from -r requirements.txt (line 7))
  Downloading pydantic_settings-2.1.0-py3-none-any.whl.metadata (2.9 kB)
Collecting geopandas==0.14.1 (from -r requirements.txt (line 10))
  Downloading geopandas-0.14.1-py3-none-any.whl.metadata (1.5 kB)
Collecting shapely==2.0.2 (from -r requirements.txt (line 11))
  Downloading shapely-2.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.0 kB)
Collecting fiona==1.9.5 (from -r requirements.txt (line 12))
  Downloading fiona-1.9.5-cp311-cp311-manylinux2014_x86_64.whl.metadata (49 kB)
Collecting pyproj==3.6.1 (from -r requirements.txt (line 13))
  Downloading pyproj-3.6.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (31 kB)
Collecting rasterio==1.3.9 (from -r requirements.txt (line 14))
  Downloading rasterio-1.3.9-cp311-cp311-manylinux2014_x86_64.whl.metadata (14 kB)
Collecting earthengine-api==0.1.383 (from -r requirements.txt (line 17))
  Downloading earthengine-api-0.1.383.tar.gz (261 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting osmnx==1.7.1 (from -r requirements.txt (line 18))
  Downloading osmnx-1.7.1-py3-none-any.whl.metadata (4.7 kB)
Collecting overpy==0.7 (from -r requirements.txt (line 19))
  Downloading overpy-0.7-py3-none-any.whl.metadata (3.5 kB)
Collecting requests==2.31.0 (from -r requirements.txt (line 20))
  Downloading requests-2.31.0-py3-none-any.whl.metadata (4.6 kB)
Collecting simplekml==1.3.6 (from -r requirements.txt (line 23))
  Downloading simplekml-1.3.6.tar.gz (52 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting ezdxf==1.1.4 (from -r requirements.txt (line 24))
  Downloading ezdxf-1.1.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.7 kB)
Collecting openpyxl==3.1.2 (from -r requirements.txt (line 25))
  Downloading openpyxl-3.1.2-py2.py3-none-any.whl.metadata (2.5 kB)
Collecting aiohttp==3.9.1 (from -r requirements.txt (line 28))
  Downloading aiohttp-3.9.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.4 kB)
Collecting aiofiles==23.2.1 (from -r requirements.txt (line 29))
  Downloading aiofiles-23.2.1-py3-none-any.whl.metadata (9.7 kB)
Collecting asyncio-throttle==1.0.2 (from -r requirements.txt (line 30))
  Downloading asyncio_throttle-1.0.2-py3-none-any.whl.metadata (3.4 kB)
Collecting python-multipart==0.0.6 (from -r requirements.txt (line 33))
  Downloading python_multipart-0.0.6-py3-none-any.whl.metadata (2.5 kB)
Collecting python-jose==3.3.0 (from python-jose[cryptography]==3.3.0->-r requirements.txt (line 34))
  Downloading python_jose-3.3.0-py2.py3-none-any.whl.metadata (5.4 kB)
Collecting python-dotenv==1.0.0 (from -r requirements.txt (line 35))
  Downloading python_dotenv-1.0.0-py3-none-any.whl.metadata (21 kB)
Collecting click==8.1.7 (from -r requirements.txt (line 36))
  Downloading click-8.1.7-py3-none-any.whl.metadata (3.0 kB)
Collecting redis==5.0.1 (from -r requirements.txt (line 39))
  Downloading redis-5.0.1-py3-none-any.whl.metadata (8.9 kB)
Collecting hiredis==2.2.3 (from -r requirements.txt (line 40))
  Downloading hiredis-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)
Collecting structlog==23.2.0 (from -r requirements.txt (line 43))
  Downloading structlog-23.2.0-py3-none-any.whl.metadata (7.6 kB)
Collecting prometheus-client==0.19.0 (from -r requirements.txt (line 44))
  Downloading prometheus_client-0.19.0-py3-none-any.whl.metadata (1.8 kB)
Collecting pandas==2.1.4 (from -r requirements.txt (line 47))
  Downloading pandas-2.1.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (18 kB)
Collecting numpy==1.25.2 (from -r requirements.txt (line 48))
  Downloading numpy-1.25.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.6 kB)
Collecting matplotlib==3.8.2 (from -r requirements.txt (line 49))
  Downloading matplotlib-3.8.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.8 kB)
Collecting folium==0.15.1 (from -r requirements.txt (line 50))
  Downloading folium-0.15.1-py2.py3-none-any.whl.metadata (3.4 kB)
Collecting anyio<4.0.0,>=3.7.1 (from fastapi==0.104.1->-r requirements.txt (line 4))
  Downloading anyio-3.7.1-py3-none-any.whl.metadata (4.7 kB)
Collecting starlette<0.28.0,>=0.27.0 (from fastapi==0.104.1->-r requirements.txt (line 4))
  Downloading starlette-0.27.0-py3-none-any.whl.metadata (5.8 kB)
Collecting typing-extensions>=4.8.0 (from fastapi==0.104.1->-r requirements.txt (line 4))
  Downloading typing_extensions-4.15.0-py3-none-any.whl.metadata (3.3 kB)
Collecting annotated-types>=0.4.0 (from pydantic==2.5.0->-r requirements.txt (line 6))
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.14.1 (from pydantic==2.5.0->-r requirements.txt (line 6))
  Downloading pydantic_core-2.14.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.5 kB)
Collecting h11>=0.8 (from uvicorn==0.24.0->uvicorn[standard]==0.24.0->-r requirements.txt (line 5))
  Downloading h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting packaging (from geopandas==0.14.1->-r requirements.txt (line 10))
  Downloading packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting attrs>=19.2.0 (from fiona==1.9.5->-r requirements.txt (line 12))
  Downloading attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting certifi (from fiona==1.9.5->-r requirements.txt (line 12))
  Downloading certifi-2025.8.3-py3-none-any.whl.metadata (2.4 kB)
Collecting click-plugins>=1.0 (from fiona==1.9.5->-r requirements.txt (line 12))
  Downloading click_plugins-1.1.1.2-py2.py3-none-any.whl.metadata (6.5 kB)
Collecting cligj>=0.5 (from fiona==1.9.5->-r requirements.txt (line 12))
  Downloading cligj-0.7.2-py3-none-any.whl.metadata (5.0 kB)
Collecting six (from fiona==1.9.5->-r requirements.txt (line 12))
  Downloading six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: setuptools in /usr/local/lib/python3.11/site-packages (from fiona==1.9.5->-r requirements.txt (line 12)) (65.5.1)
Collecting affine (from rasterio==1.3.9->-r requirements.txt (line 14))
  Downloading affine-2.4.0-py3-none-any.whl.metadata (4.0 kB)
Collecting snuggs>=1.4.1 (from rasterio==1.3.9->-r requirements.txt (line 14))
  Downloading snuggs-1.4.7-py3-none-any.whl.metadata (3.4 kB)
Collecting google-cloud-storage (from earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading google_cloud_storage-3.3.1-py3-none-any.whl.metadata (13 kB)
Collecting google-api-python-client>=1.12.1 (from earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading google_api_python_client-2.181.0-py3-none-any.whl.metadata (7.0 kB)
Collecting google-auth>=1.4.1 (from earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading google_auth-2.40.3-py2.py3-none-any.whl.metadata (6.2 kB)
Collecting google-auth-httplib2>=0.0.3 (from earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading google_auth_httplib2-0.2.0-py2.py3-none-any.whl.metadata (2.2 kB)
Collecting httplib2<1dev,>=0.9.2 (from earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading httplib2-0.30.0-py3-none-any.whl.metadata (2.2 kB)
Collecting networkx>=2.5 (from osmnx==1.7.1->-r requirements.txt (line 18))
  Downloading networkx-3.5-py3-none-any.whl.metadata (6.3 kB)
Collecting charset-normalizer<4,>=2 (from requests==2.31.0->-r requirements.txt (line 20))
  Downloading charset_normalizer-3.4.3-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl.metadata (36 kB)
Collecting idna<4,>=2.5 (from requests==2.31.0->-r requirements.txt (line 20))
  Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)
Collecting urllib3<3,>=1.21.1 (from requests==2.31.0->-r requirements.txt (line 20))
  Downloading urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
Collecting pyparsing>=2.0.1 (from ezdxf==1.1.4->-r requirements.txt (line 24))
  Downloading pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)
Collecting fonttools (from ezdxf==1.1.4->-r requirements.txt (line 24))
  Downloading fonttools-4.59.2-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (109 kB)
Collecting et-xmlfile (from openpyxl==3.1.2->-r requirements.txt (line 25))
  Downloading et_xmlfile-2.0.0-py3-none-any.whl.metadata (2.7 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp==3.9.1->-r requirements.txt (line 28))
  Downloading multidict-6.6.4-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl.metadata (5.3 kB)
Collecting yarl<2.0,>=1.0 (from aiohttp==3.9.1->-r requirements.txt (line 28))
  Downloading yarl-1.20.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (73 kB)
Collecting frozenlist>=1.1.1 (from aiohttp==3.9.1->-r requirements.txt (line 28))
  Downloading frozenlist-1.7.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (18 kB)
Collecting aiosignal>=1.1.2 (from aiohttp==3.9.1->-r requirements.txt (line 28))
  Downloading aiosignal-1.4.0-py3-none-any.whl.metadata (3.7 kB)
Collecting ecdsa!=0.15 (from python-jose==3.3.0->python-jose[cryptography]==3.3.0->-r requirements.txt (line 34))
  Downloading ecdsa-0.19.1-py2.py3-none-any.whl.metadata (29 kB)
Collecting rsa (from python-jose==3.3.0->python-jose[cryptography]==3.3.0->-r requirements.txt (line 34))
  Downloading rsa-4.9.1-py3-none-any.whl.metadata (5.6 kB)
Collecting pyasn1 (from python-jose==3.3.0->python-jose[cryptography]==3.3.0->-r requirements.txt (line 34))
  Downloading pyasn1-0.6.1-py3-none-any.whl.metadata (8.4 kB)
Collecting python-dateutil>=2.8.2 (from pandas==2.1.4->-r requirements.txt (line 47))
  Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting pytz>=2020.1 (from pandas==2.1.4->-r requirements.txt (line 47))
  Downloading pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.1 (from pandas==2.1.4->-r requirements.txt (line 47))
  Downloading tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting contourpy>=1.0.1 (from matplotlib==3.8.2->-r requirements.txt (line 49))
  Downloading contourpy-1.3.3-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (5.5 kB)
Collecting cycler>=0.10 (from matplotlib==3.8.2->-r requirements.txt (line 49))
  Downloading cycler-0.12.1-py3-none-any.whl.metadata (3.8 kB)
Collecting kiwisolver>=1.3.1 (from matplotlib==3.8.2->-r requirements.txt (line 49))
  Downloading kiwisolver-1.4.9-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (6.3 kB)
Collecting pillow>=8 (from matplotlib==3.8.2->-r requirements.txt (line 49))
  Downloading pillow-11.3.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (9.0 kB)
Collecting branca>=0.6.0 (from folium==0.15.1->-r requirements.txt (line 50))
  Downloading branca-0.8.1-py3-none-any.whl.metadata (1.5 kB)
Collecting jinja2>=2.9 (from folium==0.15.1->-r requirements.txt (line 50))
  Downloading jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Collecting xyzservices (from folium==0.15.1->-r requirements.txt (line 50))
  Downloading xyzservices-2025.4.0-py3-none-any.whl.metadata (4.3 kB)
Collecting cryptography>=3.4.0 (from python-jose[cryptography]==3.3.0->-r requirements.txt (line 34))
  Downloading cryptography-45.0.7-cp311-abi3-manylinux_2_34_x86_64.whl.metadata (5.7 kB)
Collecting httptools>=0.5.0 (from uvicorn[standard]==0.24.0->-r requirements.txt (line 5))
  Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)
Collecting pyyaml>=5.1 (from uvicorn[standard]==0.24.0->-r requirements.txt (line 5))
  Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)
Collecting uvloop!=0.15.0,!=0.15.1,>=0.14.0 (from uvicorn[standard]==0.24.0->-r requirements.txt (line 5))
  Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)
Collecting watchfiles>=0.13 (from uvicorn[standard]==0.24.0->-r requirements.txt (line 5))
  Downloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)
Collecting websockets>=10.4 (from uvicorn[standard]==0.24.0->-r requirements.txt (line 5))
  Downloading websockets-15.0.1-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Collecting sniffio>=1.1 (from anyio<4.0.0,>=3.7.1->fastapi==0.104.1->-r requirements.txt (line 4))
  Downloading sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting propcache>=0.2.1 (from yarl<2.0,>=1.0->aiohttp==3.9.1->-r requirements.txt (line 28))
  Downloading propcache-0.3.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting cffi>=1.14 (from cryptography>=3.4.0->python-jose[cryptography]==3.3.0->-r requirements.txt (line 34))
  Downloading cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (1.5 kB)
Collecting pycparser (from cffi>=1.14->cryptography>=3.4.0->python-jose[cryptography]==3.3.0->-r requirements.txt (line 34))
  Downloading pycparser-2.22-py3-none-any.whl.metadata (943 bytes)
Collecting google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5 (from google-api-python-client>=1.12.1->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading google_api_core-2.25.1-py3-none-any.whl.metadata (3.0 kB)
Collecting uritemplate<5,>=3.0.1 (from google-api-python-client>=1.12.1->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading uritemplate-4.2.0-py3-none-any.whl.metadata (2.6 kB)
Collecting googleapis-common-protos<2.0.0,>=1.56.2 (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client>=1.12.1->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading googleapis_common_protos-1.70.0-py3-none-any.whl.metadata (9.3 kB)
Collecting protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.19.5 (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client>=1.12.1->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading protobuf-6.32.0-cp39-abi3-manylinux2014_x86_64.whl.metadata (593 bytes)
Collecting proto-plus<2.0.0,>=1.22.3 (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client>=1.12.1->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading proto_plus-1.26.1-py3-none-any.whl.metadata (2.2 kB)
Collecting cachetools<6.0,>=2.0.0 (from google-auth>=1.4.1->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading cachetools-5.5.2-py3-none-any.whl.metadata (5.4 kB)
Collecting pyasn1-modules>=0.2.1 (from google-auth>=1.4.1->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading pyasn1_modules-0.4.2-py3-none-any.whl.metadata (3.5 kB)
Collecting MarkupSafe>=2.0 (from jinja2>=2.9->folium==0.15.1->-r requirements.txt (line 50))
  Downloading MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.0 kB)
Collecting google-cloud-core<3.0.0,>=2.4.2 (from google-cloud-storage->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading google_cloud_core-2.4.3-py2.py3-none-any.whl.metadata (2.7 kB)
Collecting google-resumable-media<3.0.0,>=2.7.2 (from google-cloud-storage->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading google_resumable_media-2.7.2-py2.py3-none-any.whl.metadata (2.2 kB)
Collecting google-crc32c<2.0.0,>=1.1.3 (from google-cloud-storage->earthengine-api==0.1.383->-r requirements.txt (line 17))
  Downloading google_crc32c-1.7.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.3 kB)
Downloading fastapi-0.104.1-py3-none-any.whl (92 kB)
Downloading pydantic-2.5.0-py3-none-any.whl (407 kB)
Downloading uvicorn-0.24.0-py3-none-any.whl (59 kB)
Downloading pydantic_settings-2.1.0-py3-none-any.whl (11 kB)
Downloading geopandas-0.14.1-py3-none-any.whl (1.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.1/1.1 MB 494.8 MB/s  0:00:00
Downloading shapely-2.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.5/2.5 MB 301.1 MB/s  0:00:00
Downloading fiona-1.9.5-cp311-cp311-manylinux2014_x86_64.whl (15.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 15.7/15.7 MB 304.8 MB/s  0:00:00
Downloading click-8.1.7-py3-none-any.whl (97 kB)
Downloading pyproj-3.6.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (8.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 8.6/8.6 MB 295.9 MB/s  0:00:00
Downloading rasterio-1.3.9-cp311-cp311-manylinux2014_x86_64.whl (20.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 20.6/20.6 MB 246.4 MB/s  0:00:00
Downloading osmnx-1.7.1-py3-none-any.whl (102 kB)
Downloading overpy-0.7-py3-none-any.whl (14 kB)
Downloading requests-2.31.0-py3-none-any.whl (62 kB)
Downloading ezdxf-1.1.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.6/4.6 MB 344.7 MB/s  0:00:00
Downloading openpyxl-3.1.2-py2.py3-none-any.whl (249 kB)
Downloading aiohttp-3.9.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.3/1.3 MB 660.0 MB/s  0:00:00
Downloading aiofiles-23.2.1-py3-none-any.whl (15 kB)
Downloading asyncio_throttle-1.0.2-py3-none-any.whl (4.1 kB)
Downloading python_multipart-0.0.6-py3-none-any.whl (45 kB)
Downloading python_jose-3.3.0-py2.py3-none-any.whl (33 kB)
Downloading python_dotenv-1.0.0-py3-none-any.whl (19 kB)
Downloading redis-5.0.1-py3-none-any.whl (250 kB)
Downloading hiredis-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (166 kB)
Downloading structlog-23.2.0-py3-none-any.whl (62 kB)
Downloading prometheus_client-0.19.0-py3-none-any.whl (54 kB)
Downloading pandas-2.1.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (12.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 12.2/12.2 MB 279.2 MB/s  0:00:00
Downloading numpy-1.25.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (18.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 18.2/18.2 MB 283.1 MB/s  0:00:00
Downloading matplotlib-3.8.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (11.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 11.6/11.6 MB 331.6 MB/s  0:00:00
Downloading folium-0.15.1-py2.py3-none-any.whl (97 kB)
Downloading pydantic_core-2.14.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.1/2.1 MB 385.2 MB/s  0:00:00
Downloading anyio-3.7.1-py3-none-any.whl (80 kB)
Downloading charset_normalizer-3.4.3-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl (150 kB)
Downloading httplib2-0.30.0-py3-none-any.whl (91 kB)
Downloading idna-3.10-py3-none-any.whl (70 kB)
Downloading multidict-6.6.4-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl (246 kB)
Downloading pyparsing-3.2.3-py3-none-any.whl (111 kB)
Downloading starlette-0.27.0-py3-none-any.whl (66 kB)
Downloading urllib3-2.5.0-py3-none-any.whl (129 kB)
Downloading yarl-1.20.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (348 kB)
Downloading aiosignal-1.4.0-py3-none-any.whl (7.5 kB)
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
Downloading branca-0.8.1-py3-none-any.whl (26 kB)
Downloading certifi-2025.8.3-py3-none-any.whl (161 kB)
Downloading click_plugins-1.1.1.2-py2.py3-none-any.whl (11 kB)
Downloading cligj-0.7.2-py3-none-any.whl (7.1 kB)
Downloading contourpy-1.3.3-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (355 kB)
Downloading cryptography-45.0.7-cp311-abi3-manylinux_2_34_x86_64.whl (4.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.5/4.5 MB 709.2 MB/s  0:00:00
Downloading cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (467 kB)
Downloading cycler-0.12.1-py3-none-any.whl (8.3 kB)
Downloading ecdsa-0.19.1-py2.py3-none-any.whl (150 kB)
Downloading fonttools-4.59.2-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (5.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 5.0/5.0 MB 623.7 MB/s  0:00:00
Downloading frozenlist-1.7.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (235 kB)
Downloading google_api_python_client-2.181.0-py3-none-any.whl (14.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 14.1/14.1 MB 403.7 MB/s  0:00:00
Downloading google_api_core-2.25.1-py3-none-any.whl (160 kB)
Downloading google_auth-2.40.3-py2.py3-none-any.whl (216 kB)
Downloading cachetools-5.5.2-py3-none-any.whl (10 kB)
Downloading google_auth_httplib2-0.2.0-py2.py3-none-any.whl (9.3 kB)
Downloading googleapis_common_protos-1.70.0-py3-none-any.whl (294 kB)
Downloading proto_plus-1.26.1-py3-none-any.whl (50 kB)
Downloading protobuf-6.32.0-cp39-abi3-manylinux2014_x86_64.whl (322 kB)
Downloading rsa-4.9.1-py3-none-any.whl (34 kB)
Downloading uritemplate-4.2.0-py3-none-any.whl (11 kB)
Downloading h11-0.16.0-py3-none-any.whl (37 kB)
Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (459 kB)
Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
Downloading kiwisolver-1.4.9-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (1.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.4/1.4 MB 937.2 MB/s  0:00:00
Downloading MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (23 kB)
Downloading networkx-3.5-py3-none-any.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 631.1 MB/s  0:00:00
Downloading packaging-25.0-py3-none-any.whl (66 kB)
Downloading pillow-11.3.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (6.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.6/6.6 MB 651.3 MB/s  0:00:00
Downloading propcache-0.3.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (213 kB)
Downloading pyasn1-0.6.1-py3-none-any.whl (83 kB)
Downloading pyasn1_modules-0.4.2-py3-none-any.whl (181 kB)
Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)
Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (762 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 763.0/763.0 kB 695.2 MB/s  0:00:00
Downloading six-1.17.0-py2.py3-none-any.whl (11 kB)
Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading snuggs-1.4.7-py3-none-any.whl (5.4 kB)
Downloading typing_extensions-4.15.0-py3-none-any.whl (44 kB)
Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.0/4.0 MB 392.2 MB/s  0:00:00
Downloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (453 kB)
Downloading websockets-15.0.1-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (182 kB)
Downloading affine-2.4.0-py3-none-any.whl (15 kB)
Downloading et_xmlfile-2.0.0-py3-none-any.whl (18 kB)
Downloading google_cloud_storage-3.3.1-py3-none-any.whl (275 kB)
Downloading google_cloud_core-2.4.3-py2.py3-none-any.whl (29 kB)
Downloading google_crc32c-1.7.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (32 kB)
Downloading google_resumable_media-2.7.2-py2.py3-none-any.whl (81 kB)
Downloading pycparser-2.22-py3-none-any.whl (117 kB)
Downloading xyzservices-2025.4.0-py3-none-any.whl (90 kB)
Building wheels for collected packages: earthengine-api, simplekml
  Building wheel for earthengine-api (pyproject.toml): started
  Building wheel for earthengine-api (pyproject.toml): finished with status 'done'
  Created wheel for earthengine-api: filename=earthengine_api-0.1.383-py3-none-any.whl size=294291 sha256=fb9169f49cf818c7fba91648ad31b1b6419d681a08a010fa2e330a1b53705b87
  Stored in directory: /tmp/pip-ephem-wheel-cache-k_dt9z5z/wheels/84/e8/71/dc121e977f6f47fbf0d12f0ff1c0cb81f214729460c8428467
  DEPRECATION: Building 'simplekml' using the legacy setup.py bdist_wheel mechanism, which will be removed in a future version. pip 25.3 will enforce this behaviour change. A possible replacement is to use the standardized build interface by setting the `--use-pep517` option, (possibly combined with `--no-build-isolation`), or adding a `pyproject.toml` file to the source tree of 'simplekml'. Discussion can be found at https://github.com/pypa/pip/issues/6334
  Building wheel for simplekml (setup.py): started
  Building wheel for simplekml (setup.py): finished with status 'done'
  Created wheel for simplekml: filename=simplekml-1.3.6-py3-none-any.whl size=65860 sha256=ee1d9e1a09857a82cafe1c505b876b898836e8bd652d38d035ea11631ef4715c
  Stored in directory: /tmp/pip-ephem-wheel-cache-k_dt9z5z/wheels/72/3e/80/c3e5c354c3cbe62d8c5e4fb63d9e7cdccc7f93399997ae465f
Successfully built earthengine-api simplekml
Installing collected packages: simplekml, pytz, xyzservices, websockets, uvloop, urllib3, uritemplate, tzdata, typing-extensions, structlog, sniffio, six, redis, pyyaml, python-multipart, python-dotenv, pyparsing, pycparser, pyasn1, protobuf, propcache, prometheus-client, pillow, packaging, overpy, numpy, networkx, multidict, MarkupSafe, kiwisolver, idna, httptools, hiredis, h11, google-crc32c, frozenlist, fonttools, et-xmlfile, cycler, click, charset-normalizer, certifi, cachetools, attrs, asyncio-throttle, annotated-types, aiofiles, affine, yarl, uvicorn, snuggs, shapely, rsa, requests, python-dateutil, pyproj, pydantic-core, pyasn1-modules, proto-plus, openpyxl, jinja2, httplib2, googleapis-common-protos, google-resumable-media, ezdxf, ecdsa, contourpy, cligj, click-plugins, cffi, anyio, aiosignal, watchfiles, starlette, rasterio, python-jose, pydantic, pandas, matplotlib, google-auth, fiona, cryptography, branca, aiohttp, pydantic-settings, google-auth-httplib2, google-api-core, geopandas, folium, fastapi, osmnx, google-cloud-core, google-api-python-client, google-cloud-storage, earthengine-api

Successfully installed MarkupSafe-3.0.2 affine-2.4.0 aiofiles-23.2.1 aiohttp-3.9.1 aiosignal-1.4.0 annotated-types-0.7.0 anyio-3.7.1 asyncio-throttle-1.0.2 attrs-25.3.0 branca-0.8.1 cachetools-5.5.2 certifi-2025.8.3 cffi-1.17.1 charset-normalizer-3.4.3 click-8.1.7 click-plugins-1.1.1.2 cligj-0.7.2 contourpy-1.3.3 cryptography-45.0.7 cycler-0.12.1 earthengine-api-0.1.383 ecdsa-0.19.1 et-xmlfile-2.0.0 ezdxf-1.1.4 fastapi-0.104.1 fiona-1.9.5 folium-0.15.1 fonttools-4.59.2 frozenlist-1.7.0 geopandas-0.14.1 google-api-core-2.25.1 google-api-python-client-2.181.0 google-auth-2.40.3 google-auth-httplib2-0.2.0 google-cloud-core-2.4.3 google-cloud-storage-3.3.1 google-crc32c-1.7.1 google-resumable-media-2.7.2 googleapis-common-protos-1.70.0 h11-0.16.0 hiredis-2.2.3 httplib2-0.30.0 httptools-0.6.4 idna-3.10 jinja2-3.1.6 kiwisolver-1.4.9 matplotlib-3.8.2 multidict-6.6.4 networkx-3.5 numpy-1.25.2 openpyxl-3.1.2 osmnx-1.7.1 overpy-0.7 packaging-25.0 pandas-2.1.4 pillow-11.3.0 prometheus-client-0.19.0 propcache-0.3.2 proto-plus-1.26.1 protobuf-6.32.0 pyasn1-0.6.1 pyasn1-modules-0.4.2 pycparser-2.22 pydantic-2.5.0 pydantic-core-2.14.1 pydantic-settings-2.1.0 pyparsing-3.2.3 pyproj-3.6.1 python-dateutil-2.9.0.post0 python-dotenv-1.0.0 python-jose-3.3.0 python-multipart-0.0.6 pytz-2025.2 pyyaml-6.0.2 rasterio-1.3.9 redis-5.0.1 requests-2.31.0 rsa-4.9.1 shapely-2.0.2 simplekml-1.3.6 six-1.17.0 sniffio-1.3.1 snuggs-1.4.7 starlette-0.27.0 structlog-23.2.0 typing-extensions-4.15.0 tzdata-2025.2 uritemplate-4.2.0 urllib3-2.5.0 uvicorn-0.24.0 uvloop-0.21.0 watchfiles-1.1.0 websockets-15.0.1 xyzservices-2025.4.0 yarl-1.20.1
DONE 28.3s

--> COPY --chown=user ./src src
DONE 0.1s

--> COPY --chown=user ./.env.template .env
DONE 0.0s

--> COPY --chown=user ./app.py app.py
DONE 0.0s

--> RUN mkdir -p     /home/<USER>/app/temp_exports     /home/<USER>/app/logs     /home/<USER>/.cache
DONE 0.0s

--> Pushing image
DONE 7.1s

--> Exporting cache
DONE 0.2s




===== Application Startup at 2025-09-07 09:12:06 =====

Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/__main__.py", line 4, in <module>
    uvicorn.main()
  File "/home/<USER>/.local/lib/python3.11/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/main.py", line 416, in main
    run(
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/app/src/main.py", line 19, in <module>
    from src.api.routes import extract, export, health
  File "/home/<USER>/app/src/api/routes/extract.py", line 11, in <module>
    from src.api.schemas import (
  File "/home/<USER>/app/src/api/schemas.py", line 11, in <module>
    from geojson_pydantic import Polygon, FeatureCollection
ModuleNotFoundError: No module named 'geojson_pydantic'
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/__main__.py", line 4, in <module>
    uvicorn.main()
  File "/home/<USER>/.local/lib/python3.11/site-packages/click/core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/click/core.py", line 1078, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/click/core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/click/core.py", line 783, in invoke
    return __callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/main.py", line 416, in main
    run(
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/.local/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/app/src/main.py", line 19, in <module>
    from src.api.routes import extract, export, health
  File "/home/<USER>/app/src/api/routes/extract.py", line 11, in <module>
    from src.api.schemas import (
  File "/home/<USER>/app/src/api/schemas.py", line 11, in <module>
    from geojson_pydantic import Polygon, FeatureCollection
ModuleNotFoundError: No module named 'geojson_pydantic'