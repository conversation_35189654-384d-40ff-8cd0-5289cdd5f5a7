===== Application Startup at 2025-09-07 11:00:21 =====

🌍 Atlas GIS API - Configuration Status
==================================================
Platform: huggingface_spaces
Environment: healthy
Space ID: kamau1/atlas
Author: kamau1

🔧 Configuration:
   Service: atlas-geospatial-api v1.0.0
   Debug mode: False
   Log level: INFO
   Max AOI area: 100.0 km²
   Request timeout: 300s
   Overpass rate limit: 0.5 req/s

🔑 Google Earth Engine: complete
   Project: grand-airport-464420-e3
   Service Account: <EMAIL>

✅ Configuration Info:
   • Google Earth Engine credentials configured

💡 Recommendations:
   ✅ Running on HuggingFace Spaces - environment variables are injected at runtime
   🔧 Use Space Settings to configure environment variables
   🔒 Use Secrets for sensitive data like Google credentials
   📝 Variables are automatically available as environment variables
==================================================
INFO:src.main:Mounting static files from: src/web/static (exists: True)
INFO:     Started server process [1]
INFO:     Waiting for application startup.
INFO:src.main:Starting Atlas FastAPI Service
INFO:src.main:Service configuration: atlas-geospatial-api v1.0.0
INFO:src.utils.gee_auth:🚀 Starting Google Earth Engine initialization...
INFO:src.utils.gee_auth:🔧 Creating service account credentials from environment variables
INFO:src.utils.gee_auth:   Project ID: grand-airport-464420-e3
INFO:src.utils.gee_auth:   Client Email: <EMAIL>
INFO:src.utils.gee_auth:   Client ID: 116275573697305905318
INFO:src.utils.gee_auth:   Private Key ID: 5ff05269...
INFO:src.utils.gee_auth:   Private Key Length: 1703 chars
INFO:src.utils.gee_auth:✅ Service account credentials file created: /tmp/tmp6favxg0m.json
INFO:src.utils.gee_auth:📁 Using credentials file: /tmp/tmp6favxg0m.json
INFO:src.utils.gee_auth:🔍 Service Account Key Data:
INFO:src.utils.gee_auth:   Response Type: <class 'dict'>
INFO:src.utils.gee_auth:   Response Content: {'type': 'service_account', 'project_id': 'grand-airport-464420-e3', 'private_key_id': '5ff05269086d4d1425998a76df3d603f81cb45d4', 'private_key': '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCvmdH0UiM/u/Vg\nZZN5xHpt5AGfRF2TnD6tg8EGDtxtM/PGmtVCBy62aU0tmgf3Y4kcF48B3ytbJ1Km\nA2VcM8p6B7cXkIVJj7TWPT0IqKc33W5AEZd5AHxjKxofIXaJVj2M4c617ngO2qPy\nwOk1JTPK64zRM+Dp6uA+IczvI4dGaNWSP4dmny4gNvqkUrMrM+hOW2MgqVyY5eD8\nL4q+fGzAWV8bP7p8uA+bZH38dmr3YYIJ2Y9Ss7lLEgvM1h6Ml6uDJofzkJea/06A\niyjW3wiIGTo/kuBpfVmq0QT7m/tXcJ0KZj+lgG2Sr3+xSSnw642zUCm0yqRyndnq\n1MZha6bdAgMBAAECggEALCJNbgoc2N5tOAqfi7mxiaAGNljQ6hIZgLO+Fj8y8HFf\nwhlVIxC6EVea8aymdut54Okc5LwJ4hGaiKYO2iMcLxp0ToRgs5HeQgBHMD9vM6/A\nD3M8bkmMecIyAQQAjljyBFAWi4xBeX5DOXZ5xoUwpL4Z+NSAoVoKfSMypiH4VF4H\nBI4r0Ik2m1urMtuOTvkFSJOpENKQrz/z1QrHIrk8FSYOZUatsQjW4QyuGVg8csMm\nIfgETsoKGodiGjKLEAl5DspEOFh4fdGSlLQVFXTNPp9+QN6xZXVY/z1VEEbAkNkL\nTnasUg5kZ9Jp97NPMVn2HdUpHcCDywkJ612bnodfcwKBgQDja63ojNpv3RRWNgod\nvYmLUBIoh0XJPW1kXCzI4NUiHobmODwF8FZimpnHOE9LiWtatAajdH/F4ShULRfV\nheGAZDDO+sCaEgmeuQN7kT6fo+T525cwMpnvrRIh46QT7P1/a5fp8KSFjMba9qKl\nWaC7HJOCG8hXEZ4wqQlLUgJm5wKBgQDFqw27r4RlqfrufurS83AnjPZjSdPT3AYu\nkr50iV8KMGT+Yyo0CzgkIlyUvpFq7r2y8HzeaWBJOlDYGwMrzlcYuQ+9XF0fO7sE\nZL4VvKYkQFlSNC1DXxd4/uID2r5YvR2n18x/FV8azaBQdWgUYg78hjoeRRx7Z97w\nLTeBbie/mwKBgBsZHIrGJc0IecDgBYqPqBD6hH9hxvX4pM09knSJHbxICOjrsHNA\ntPpg04wCcKhegnU3WEcnvYXVk1mcWeeuYTVMOuiexk5VceIAx6J0pxKck88V1EkA\n7oi5hZyWJFnfdkE1j89nRiNHp/Y1RyIOsPE6sXTiXJfLWJ8RtTLNXc3nAoGBAJS4\nKdD4MUi8JwuhedPVTKC1Mftp5HIVrKEFzz2mzVbBKnsq6OnXNxoYsEdJSeeGUDt9\nLLW52rO9qG2vhSdES8tSEUGLaJTDP+YCVfHMycOklo9+qj9yIBrsyefUgDoLr3BE\nsJs83R0xcFiYvQH49QWRh9CWduNyNw3FN2muE0HZAoGAfU3KVO/Wh05fKfvJqFV8\nWEs8yWFDIgZLt5vdw0sh+ofRXq1q7drimnTf9slsRgy4sNLcXaNCm8zm7oHomg9p\nlOj/0twZ3L+t3n4lUhWFZhGCigtN0xUjhhr/JNJH7ZkQF3zyvgKiSAS1+H59Ci0i\nW0Wm3nuTEGg0VrFl00QXA48=\n-----END PRIVATE KEY-----', 'client_email': '<EMAIL>', 'client_id': '116275573697305905318', 'auth_uri': 'https://accounts.google.com/o/oauth2/auth', 'token_uri': 'https://oauth2.googleapis.com/token', 'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs', 'universe_domain': 'googleapis.com', 'client_x509_cert_url': 'https://www.googleapis.com/robot/v1/metadata/x509/atlas-gee-service-account%40grand-airport-464420-e3.iam.gserviceaccount.com'}
INFO:src.utils.gee_auth:   type: service_account
INFO:src.utils.gee_auth:   project_id: grand-airport-464420-e3
INFO:src.utils.gee_auth:   private_key_id: 5ff05269086d4d1425998a76df3d603f81cb45d4
INFO:src.utils.gee_auth:   private_key: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************INFO:src.utils.gee_auth:   client_email: <EMAIL>
INFO:src.utils.gee_auth:   client_id: 116275573697305905318
INFO:src.utils.gee_auth:   auth_uri: https://accounts.google.com/o/oauth2/auth
INFO:src.utils.gee_auth:   token_uri: https://oauth2.googleapis.com/token
INFO:src.utils.gee_auth:   auth_provider_x509_cert_url: https://www.googleapis.com/oauth2/v1/certs
INFO:src.utils.gee_auth:   universe_domain: googleapis.com
INFO:src.utils.gee_auth:   client_x509_cert_url: https://www.googleapis.com/robot/v1/metadata/x509/atlas-gee-service-account%40grand-airport-464420-e3.iam.gserviceaccount.com
INFO:src.utils.gee_auth:🔐 Creating service account credentials...
INFO:src.utils.gee_auth:🎯 Using OAuth scopes: ['https://www.googleapis.com/auth/earthengine', 'https://www.googleapis.com/auth/earthengine.readonly', 'https://www.googleapis.com/auth/devstorage.full_control', 'https://www.googleapis.com/auth/cloud-platform']
INFO:src.utils.gee_auth:🔧 Attempting authentication with google.oauth2.service_account...
INFO:src.utils.gee_auth:✅ Service account credentials created successfully
INFO:src.utils.gee_auth:🔍 Google Auth Credentials Object:
INFO:src.utils.gee_auth:   Response Type: <class 'google.oauth2.service_account.Credentials'>
INFO:src.utils.gee_auth:   Response Content: <google.oauth2.service_account.Credentials object at 0x7f2fa25f3390>
INFO:src.utils.gee_auth:   Response Attributes: {'token': None, 'expiry': None, '_quota_project_id': None, '_trust_boundary': {'locations': [], 'encoded_locations': '0x0'}, '_universe_domain': 'googleapis.com', '_use_non_blocking_refresh': False, '_refresh_worker': <google.auth._refresh_worker.RefreshThreadManager object at 0x7f2fa223ab90>, '_scopes': ['https://www.googleapis.com/auth/earthengine', 'https://www.googleapis.com/auth/earthengine.readonly', 'https://www.googleapis.com/auth/devstorage.full_control', 'https://www.googleapis.com/auth/cloud-platform'], '_default_scopes': None, '_cred_file_path': None, '_signer': <google.auth.crypt._cryptography_rsa.RSASigner object at 0x7f2fa2301490>, '_service_account_email': '<EMAIL>', '_subject': None, '_project_id': 'grand-airport-464420-e3', '_token_uri': 'https://oauth2.googleapis.com/token', '_always_use_jwt_access': False, '_jwt_credentials': None, '_additional_claims': {}}
INFO:src.utils.gee_auth:🌍 Initializing Earth Engine with project: grand-airport-464420-e3
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:google_auth_httplib2:Making request: POST https://oauth2.googleapis.com/token
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): oauth2.googleapis.com:443
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): earthengine.googleapis.com:443
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json HTTP/1.1" 403 None
WARNING:googleapiclient.http:Encountered 403 Forbidden with reason "PERMISSION_DENIED"
ERROR:src.utils.gee_auth:❌ google.oauth2.service_account failed: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.
INFO:src.utils.gee_auth:🔍 Google Auth Error:
INFO:src.utils.gee_auth:   Response Type: <class 'ee.ee_exception.EEException'>
INFO:src.utils.gee_auth:   Response Content: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.
INFO:src.utils.gee_auth:   Response Attributes: {}
INFO:src.utils.gee_auth:🔧 Attempting authentication with ee.ServiceAccountCredentials...
INFO:src.utils.gee_auth:✅ EE service account credentials created
INFO:src.utils.gee_auth:🔍 EE Credentials Object:
INFO:src.utils.gee_auth:   Response Type: <class 'google.oauth2.service_account.Credentials'>
INFO:src.utils.gee_auth:   Response Content: <google.oauth2.service_account.Credentials object at 0x7f2fa1d6ac10>
INFO:src.utils.gee_auth:   Response Attributes: {'token': None, 'expiry': None, '_quota_project_id': None, '_trust_boundary': {'locations': [], 'encoded_locations': '0x0'}, '_universe_domain': 'googleapis.com', '_use_non_blocking_refresh': False, '_refresh_worker': <google.auth._refresh_worker.RefreshThreadManager object at 0x7f2fa1d6ac90>, '_scopes': ['https://www.googleapis.com/auth/earthengine', 'https://www.googleapis.com/auth/devstorage.full_control'], '_default_scopes': None, '_cred_file_path': None, '_signer': <google.auth.crypt._cryptography_rsa.RSASigner object at 0x7f2fa1d4bb50>, '_service_account_email': '<EMAIL>', '_subject': None, '_project_id': 'grand-airport-464420-e3', '_token_uri': 'https://oauth2.googleapis.com/token', '_always_use_jwt_access': False, '_jwt_credentials': None, '_additional_claims': {}}
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:google_auth_httplib2:Making request: POST https://oauth2.googleapis.com/token
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json HTTP/1.1" 403 None
WARNING:googleapiclient.http:Encountered 403 Forbidden with reason "PERMISSION_DENIED"
ERROR:src.utils.gee_auth:❌ ee.ServiceAccountCredentials failed: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.
INFO:src.utils.gee_auth:🔍 EE Auth Error:
INFO:src.utils.gee_auth:   Response Type: <class 'ee.ee_exception.EEException'>
INFO:src.utils.gee_auth:   Response Content: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.
INFO:src.utils.gee_auth:   Response Attributes: {}
ERROR:src.utils.gee_auth:❌ Service account authentication failed: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.
ERROR:src.utils.gee_auth:   Full error details: Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 379, in _execute_cloud_call
    return call.execute(num_retries=num_retries)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/http.py", line 938, in execute
    raise HttpError(resp, content, uri=self.uri)
googleapiclient.errors.HttpError: <HttpError 403 when requesting https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json returned "Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'USER_PROJECT_DENIED', 'domain': 'googleapis.com', 'metadata': {'consumer': 'projects/grand-airport-464420-e3', 'service': 'earthengine.googleapis.com', 'containerInfo': 'grand-airport-464420-e3'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.'}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Google developer console IAM admin', 'url': 'https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3'}]}]">

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/app/src/utils/gee_auth.py", line 168, in initialize_earth_engine
    ee.Initialize(credentials, project=settings.google_cloud_project)
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/_utils.py", line 39, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/__init__.py", line 151, in Initialize
    ApiFunction.initialize()
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/apifunction.py", line 162, in initialize
    signatures = data.getAlgorithms()
                 ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 1445, in getAlgorithms
    return _cloud_api_utils.convert_algorithms(_execute_cloud_call(call))
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 381, in _execute_cloud_call
    raise _translate_cloud_exception(e)  # pylint: disable=raise-missing-from
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ee.ee_exception.EEException: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 379, in _execute_cloud_call
    return call.execute(num_retries=num_retries)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/http.py", line 938, in execute
    raise HttpError(resp, content, uri=self.uri)
googleapiclient.errors.HttpError: <HttpError 403 when requesting https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json returned "Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'USER_PROJECT_DENIED', 'domain': 'googleapis.com', 'metadata': {'containerInfo': 'grand-airport-464420-e3', 'service': 'earthengine.googleapis.com', 'consumer': 'projects/grand-airport-464420-e3'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.'}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Google developer console IAM admin', 'url': 'https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3'}]}]">

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/app/src/utils/gee_auth.py", line 202, in initialize_earth_engine
    raise ee_auth_error
  File "/home/<USER>/app/src/utils/gee_auth.py", line 192, in initialize_earth_engine
    ee.Initialize(credentials, project=settings.google_cloud_project)
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/_utils.py", line 39, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/__init__.py", line 151, in Initialize
    ApiFunction.initialize()
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/apifunction.py", line 162, in initialize
    signatures = data.getAlgorithms()
                 ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 1445, in getAlgorithms
    return _cloud_api_utils.convert_algorithms(_execute_cloud_call(call))
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 381, in _execute_cloud_call
    raise _translate_cloud_exception(e)  # pylint: disable=raise-missing-from
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ee.ee_exception.EEException: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.

INFO:src.utils.gee_auth:🔍 Credentials Error:
INFO:src.utils.gee_auth:   Response Type: <class 'ee.ee_exception.EEException'>
INFO:src.utils.gee_auth:   Response Content: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.
INFO:src.utils.gee_auth:   Response Attributes: {}
INFO:src.utils.gee_auth:🔧 Attempting fallback to default authentication...
DEBUG:google.auth._default:Checking /tmp/tmp6favxg0m.json for explicit credentials as part of auth process...
DEBUG:googleapiclient.discovery:URL being requested: GET https://earthengine.googleapis.com/$discovery/rest?version=v1&prettyPrint=false
DEBUG:google_auth_httplib2:Making request: POST https://oauth2.googleapis.com/token
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 400 None
ERROR:src.utils.gee_auth:❌ Default authentication also failed: ('invalid_scope: Invalid OAuth scope or ID token audience provided.', {'error': 'invalid_scope', 'error_description': 'Invalid OAuth scope or ID token audience provided.'})
INFO:src.utils.gee_auth:🔍 Default Auth Error:
INFO:src.utils.gee_auth:   Response Type: <class 'google.auth.exceptions.RefreshError'>
INFO:src.utils.gee_auth:   Response Content: ('invalid_scope: Invalid OAuth scope or ID token audience provided.', {'error': 'invalid_scope', 'error_description': 'Invalid OAuth scope or ID token audience provided.'})
INFO:src.utils.gee_auth:   Response Attributes: {'_retryable': False}
ERROR:src.utils.gee_auth:❌ CRITICAL: Google Earth Engine initialization failed: ('invalid_scope: Invalid OAuth scope or ID token audience provided.', {'error': 'invalid_scope', 'error_description': 'Invalid OAuth scope or ID token audience provided.'})
ERROR:src.utils.gee_auth:   Full traceback: Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 379, in _execute_cloud_call
    return call.execute(num_retries=num_retries)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/http.py", line 938, in execute
    raise HttpError(resp, content, uri=self.uri)
googleapiclient.errors.HttpError: <HttpError 403 when requesting https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json returned "Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'USER_PROJECT_DENIED', 'domain': 'googleapis.com', 'metadata': {'consumer': 'projects/grand-airport-464420-e3', 'service': 'earthengine.googleapis.com', 'containerInfo': 'grand-airport-464420-e3'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.'}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Google developer console IAM admin', 'url': 'https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3'}]}]">

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/app/src/utils/gee_auth.py", line 168, in initialize_earth_engine
    ee.Initialize(credentials, project=settings.google_cloud_project)
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/_utils.py", line 39, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/__init__.py", line 151, in Initialize
    ApiFunction.initialize()
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/apifunction.py", line 162, in initialize
    signatures = data.getAlgorithms()
                 ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 1445, in getAlgorithms
    return _cloud_api_utils.convert_algorithms(_execute_cloud_call(call))
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 381, in _execute_cloud_call
    raise _translate_cloud_exception(e)  # pylint: disable=raise-missing-from
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ee.ee_exception.EEException: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 379, in _execute_cloud_call
    return call.execute(num_retries=num_retries)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/http.py", line 938, in execute
    raise HttpError(resp, content, uri=self.uri)
googleapiclient.errors.HttpError: <HttpError 403 when requesting https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json returned "Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'USER_PROJECT_DENIED', 'domain': 'googleapis.com', 'metadata': {'containerInfo': 'grand-airport-464420-e3', 'service': 'earthengine.googleapis.com', 'consumer': 'projects/grand-airport-464420-e3'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.'}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Google developer console IAM admin', 'url': 'https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3'}]}]">

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/app/src/utils/gee_auth.py", line 202, in initialize_earth_engine
    raise ee_auth_error
  File "/home/<USER>/app/src/utils/gee_auth.py", line 192, in initialize_earth_engine
    ee.Initialize(credentials, project=settings.google_cloud_project)
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/_utils.py", line 39, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/__init__.py", line 151, in Initialize
    ApiFunction.initialize()
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/apifunction.py", line 162, in initialize
    signatures = data.getAlgorithms()
                 ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 1445, in getAlgorithms
    return _cloud_api_utils.convert_algorithms(_execute_cloud_call(call))
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 381, in _execute_cloud_call
    raise _translate_cloud_exception(e)  # pylint: disable=raise-missing-from
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ee.ee_exception.EEException: Caller does not have required permission to use project grand-airport-464420-e3. Grant the caller the roles/serviceusage.serviceUsageConsumer role, or a custom role with the serviceusage.services.use permission, by visiting https://console.developers.google.com/iam-admin/iam/project?project=grand-airport-464420-e3 and then retry. Propagation of the new permission may take a few minutes.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/app/src/utils/gee_auth.py", line 219, in initialize_earth_engine
    raise default_error
  File "/home/<USER>/app/src/utils/gee_auth.py", line 212, in initialize_earth_engine
    ee.Initialize(project=settings.google_cloud_project)
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/_utils.py", line 39, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/__init__.py", line 140, in Initialize
    data.initialize(
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 225, in initialize
    _install_cloud_api_resource()
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/data.py", line 296, in _install_cloud_api_resource
    _cloud_api_resource = _cloud_api_utils.build_cloud_resource(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/_cloud_api_utils.py", line 195, in build_cloud_resource
    resource = build(static_discovery=False)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/ee/_cloud_api_utils.py", line 179, in build
    return discovery.build(
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/discovery.py", line 304, in build
    content = _retrieve_discovery_doc(
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/discovery.py", line 439, in _retrieve_discovery_doc
    resp, content = req.execute(num_retries=num_retries)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/http.py", line 923, in execute
    resp, content = _retry_request(
                    ^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/googleapiclient/http.py", line 191, in _retry_request
    resp, content = http.request(uri, method, *args, **kwargs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/google_auth_httplib2.py", line 209, in request
    self.credentials.before_request(self._request, method, uri, request_headers)
  File "/home/<USER>/.local/lib/python3.11/site-packages/google/auth/credentials.py", line 239, in before_request
    self._blocking_refresh(request)
  File "/home/<USER>/.local/lib/python3.11/site-packages/google/auth/credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
  File "/home/<USER>/.local/lib/python3.11/site-packages/google/oauth2/service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/google/oauth2/_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/google/oauth2/_client.py", line 270, in _token_endpoint_request
    _handle_error_response(response_data, retryable_error)
  File "/home/<USER>/.local/lib/python3.11/site-packages/google/oauth2/_client.py", line 69, in _handle_error_response
    raise exceptions.RefreshError(
google.auth.exceptions.RefreshError: ('invalid_scope: Invalid OAuth scope or ID token audience provided.', {'error': 'invalid_scope', 'error_description': 'Invalid OAuth scope or ID token audience provided.'})

INFO:src.utils.gee_auth:🔍 Critical Error:
INFO:src.utils.gee_auth:   Response Type: <class 'google.auth.exceptions.RefreshError'>
INFO:src.utils.gee_auth:   Response Content: ('invalid_scope: Invalid OAuth scope or ID token audience provided.', {'error': 'invalid_scope', 'error_description': 'Invalid OAuth scope or ID token audience provided.'})
INFO:src.utils.gee_auth:   Response Attributes: {'_retryable': False}
ERROR:src.utils.gee_auth:🔧 SOLUTION: OAuth scope error - check service account setup:
ERROR:src.utils.gee_auth:   1. Ensure service account has Earth Engine Resource Viewer role
ERROR:src.utils.gee_auth:   2. Enable Earth Engine API in Google Cloud Console
ERROR:src.utils.gee_auth:   3. Register project for Earth Engine at https://earthengine.google.com/
ERROR:src.utils.gee_auth:   4. Check that credentials are properly formatted
WARNING:src.main:Google Earth Engine initialization failed - some features may be unavailable
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:7860 (Press CTRL+C to quit)